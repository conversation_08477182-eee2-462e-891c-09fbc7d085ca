<?php
/**
 * CMS Management Page
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/cms_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'save_content':
                    $data = [
                        'id' => $_POST['content_id'] ?? null,
                        'section' => $_POST['section'],
                        'title' => $_POST['title'],
                        'content' => $_POST['content'],
                        'image' => $_POST['image'] ?? null,
                        'order_index' => intval($_POST['order_index'] ?? 1),
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];
                    
                    saveContentSection($data);
                    $message = 'Content saved successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'delete_content':
                    $id = $_POST['content_id'];
                    deleteContentSection($id);
                    $message = 'Content deleted successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'add_gallery_image':
                    $data = [
                        'title' => $_POST['title'],
                        'description' => $_POST['description'] ?? '',
                        'image_url' => $_POST['image_url'],
                        'category' => $_POST['category'],
                        'order_index' => intval($_POST['order_index'] ?? 0),
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];
                    
                    addGalleryImage($data);
                    $message = 'Gallery image added successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'update_gallery_image':
                    $id = $_POST['image_id'];
                    $data = [
                        'title' => $_POST['title'],
                        'description' => $_POST['description'] ?? '',
                        'image_url' => $_POST['image_url'],
                        'category' => $_POST['category'],
                        'order_index' => intval($_POST['order_index'] ?? 0),
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];
                    
                    updateGalleryImage($id, $data);
                    $message = 'Gallery image updated successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'delete_gallery_image':
                    $id = $_POST['image_id'];
                    deleteGalleryImage($id);
                    $message = 'Gallery image deleted successfully!';
                    $messageType = 'success';
                    break;

                case 'bulk_gallery_action':
                    $action = $_POST['bulk_action'];
                    $imageIds = $_POST['image_ids'] ?? [];

                    if (empty($imageIds)) {
                        $message = 'No images selected!';
                        $messageType = 'error';
                        break;
                    }

                    $count = 0;
                    foreach ($imageIds as $imageId) {
                        try {
                            switch ($action) {
                                case 'activate':
                                    updateGalleryImage($imageId, ['is_active' => 1]);
                                    $count++;
                                    break;
                                case 'deactivate':
                                    updateGalleryImage($imageId, ['is_active' => 0]);
                                    $count++;
                                    break;
                                case 'delete':
                                    deleteGalleryImage($imageId);
                                    $count++;
                                    break;
                            }
                        } catch (Exception $e) {
                            // Continue with other images if one fails
                        }
                    }

                    $message = "Successfully processed $count images!";
                    $messageType = 'success';
                    break;
                    
                case 'toggle_content_status':
                    $id = $_POST['content_id'];
                    $content = getContentSectionById($id);
                    updateContentSection($id, ['is_active' => $content['is_active'] ? 0 : 1]);
                    $message = 'Content status updated successfully!';
                    $messageType = 'success';
                    break;

                case 'toggle_image_status':
                    $id = $_POST['image_id'];
                    $image = getGalleryImageById($id);
                    if ($image) {
                        updateGalleryImage($id, ['is_active' => $image['is_active'] ? 0 : 1]);
                        $message = 'Gallery image status updated successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Gallery image not found!';
                        $messageType = 'error';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get filters
$activeTab = $_GET['tab'] ?? 'hero';
$galleryCategory = $_GET['category'] ?? '';

// Get CMS data
$cmsData = getCMSData();

$pageTitle = "Gallery & CMS";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white">Gallery & CMS</h1>
                    <p class="mt-2 text-gray-400">Manage website content and image gallery</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <div class="flex items-center space-x-4">
                        <button onclick="openContentModal()" class="bg-salon-gold hover:bg-yellow-500 text-black px-6 py-3 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-plus mr-2"></i>Add Content
                        </button>
                        <button onclick="openGalleryModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-image mr-2"></i>Add Image
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-file-alt text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Content</p>
                        <p class="text-2xl font-semibold text-white"><?= $cmsData['stats']['totalContent'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Active Content</p>
                        <p class="text-2xl font-semibold text-white"><?= $cmsData['stats']['activeContent'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-images text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Gallery Images</p>
                        <p class="text-2xl font-semibold text-white"><?= $cmsData['stats']['totalImages'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-eye text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Active Images</p>
                        <p class="text-2xl font-semibold text-white"><?= $cmsData['stats']['activeImages'] ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <div class="bg-secondary-800 rounded-lg mb-6">
            <div class="border-b border-secondary-700">
                <nav class="flex space-x-8 px-6">
                    <a href="?tab=hero" class="py-4 px-1 border-b-2 font-medium text-sm <?= $activeTab === 'hero' ? 'border-salon-gold text-salon-gold' : 'border-transparent text-gray-400 hover:text-gray-300' ?>">
                        <i class="fas fa-home mr-2"></i>Hero Section
                    </a>
                    <a href="?tab=content" class="py-4 px-1 border-b-2 font-medium text-sm <?= $activeTab === 'content' ? 'border-salon-gold text-salon-gold' : 'border-transparent text-gray-400 hover:text-gray-300' ?>">
                        <i class="fas fa-file-alt mr-2"></i>Content Sections
                    </a>
                    <a href="?tab=gallery" class="py-4 px-1 border-b-2 font-medium text-sm <?= $activeTab === 'gallery' ? 'border-salon-gold text-salon-gold' : 'border-transparent text-gray-400 hover:text-gray-300' ?>">
                        <i class="fas fa-images mr-2"></i>Gallery
                    </a>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <?php if ($activeTab === 'hero'): ?>
            <!-- Hero Section Tab -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Hero Section Content</h3>
                <?php $hero = $cmsData['heroContent']; ?>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-white mb-2">Current Hero Content</h4>
                        <div class="bg-secondary-700 rounded-lg p-4">
                            <h5 class="text-salon-gold font-semibold mb-2"><?= htmlspecialchars($hero['title']) ?></h5>
                            <p class="text-gray-300 mb-3"><?= htmlspecialchars($hero['content']) ?></p>
                            <?php if ($hero['image']): ?>
                                <div class="text-sm text-gray-400">
                                    <i class="fas fa-image mr-1"></i>
                                    Background Image: <?= htmlspecialchars($hero['image']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="mt-4">
                            <button onclick="editContent(<?= htmlspecialchars(json_encode($hero)) ?>)" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-edit mr-2"></i>Edit Hero
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-white mb-2">Hero Preview</h4>
                        <div class="bg-gradient-to-r from-salon-gold/20 to-secondary-700 rounded-lg p-6 text-center">
                            <h5 class="text-2xl font-bold text-white mb-3"><?= htmlspecialchars($hero['title']) ?></h5>
                            <p class="text-gray-300"><?= htmlspecialchars($hero['content']) ?></p>
                            <div class="mt-4">
                                <span class="inline-block bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold">
                                    Book Now
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif ($activeTab === 'content'): ?>
            <!-- Content Sections Tab -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Content Sections</h3>
                
                <!-- Content Types Overview -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <?php foreach ($cmsData['contentTypes'] as $type => $label): ?>
                        <?php 
                        $sectionContent = array_filter($cmsData['contentSections'], function($content) use ($type) {
                            return $content['section'] === $type;
                        });
                        $hasContent = !empty($sectionContent);
                        ?>
                        <div class="bg-secondary-700 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-white"><?= $label ?></h4>
                                <span class="text-xs px-2 py-1 rounded-full <?= $hasContent ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' ?>">
                                    <?= $hasContent ? 'Active' : 'Empty' ?>
                                </span>
                            </div>
                            <?php if ($hasContent): ?>
                                <?php $content = reset($sectionContent); ?>
                                <p class="text-sm text-gray-400 mb-3"><?= htmlspecialchars(substr($content['content'], 0, 100)) ?>...</p>
                                <div class="flex items-center space-x-2">
                                    <button onclick="editContent(<?= htmlspecialchars(json_encode($content)) ?>)" 
                                            class="text-blue-400 hover:text-blue-300 text-sm">
                                        <i class="fas fa-edit mr-1"></i>Edit
                                    </button>
                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure?')">
                                        <input type="hidden" name="action" value="toggle_content_status">
                                        <input type="hidden" name="content_id" value="<?= $content['id'] ?>">
                                        <button type="submit" class="text-yellow-400 hover:text-yellow-300 text-sm">
                                            <i class="fas fa-<?= $content['is_active'] ? 'eye-slash' : 'eye' ?> mr-1"></i>
                                            <?= $content['is_active'] ? 'Hide' : 'Show' ?>
                                        </button>
                                    </form>
                                </div>
                            <?php else: ?>
                                <p class="text-sm text-gray-500 mb-3">No content added yet</p>
                                <button onclick="createContent('<?= $type ?>')" 
                                        class="text-salon-gold hover:text-yellow-400 text-sm">
                                    <i class="fas fa-plus mr-1"></i>Add Content
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

        <?php else: ?>
            <!-- Gallery Tab -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Gallery Management</h3>
                    <div class="flex items-center space-x-4">
                        <!-- Search Box -->
                        <div class="relative">
                            <input type="text"
                                   id="gallerySearch"
                                   placeholder="Search images..."
                                   class="pl-10 pr-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                   onkeyup="filterGallery()">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>

                        <!-- Category Filter -->
                        <form method="GET" class="flex items-center space-x-2">
                            <input type="hidden" name="tab" value="gallery">
                            <select name="category" onchange="this.form.submit()"
                                    class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Categories</option>
                                <?php foreach ($cmsData['galleryCategories'] as $category): ?>
                                    <option value="<?= htmlspecialchars($category) ?>" <?= $galleryCategory === $category ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </form>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="mb-4 p-4 bg-secondary-700 rounded-lg" id="bulkActionsPanel" style="display: none;">
                    <form method="POST" onsubmit="return confirmBulkAction()">
                        <input type="hidden" name="action" value="bulk_gallery_action">
                        <input type="hidden" name="tab" value="gallery">
                        <div class="flex items-center space-x-4">
                            <span class="text-white font-medium">Bulk Actions:</span>
                            <select name="bulk_action" required class="px-3 py-2 bg-secondary-600 border border-secondary-500 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">Select Action</option>
                                <option value="activate">Activate Selected</option>
                                <option value="deactivate">Deactivate Selected</option>
                                <option value="delete">Delete Selected</option>
                            </select>
                            <button type="submit" class="bg-salon-gold hover:bg-yellow-500 text-black px-4 py-2 rounded-lg font-semibold transition-colors">
                                Apply
                            </button>
                            <button type="button" onclick="clearSelection()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                                Clear Selection
                            </button>
                        </div>
                        <div id="selectedImagesContainer"></div>
                    </form>
                </div>

                <!-- Gallery Grid -->
                <?php 
                $filteredImages = $galleryCategory 
                    ? array_filter($cmsData['galleryImages'], function($img) use ($galleryCategory) {
                        return $img['category'] === $galleryCategory;
                    })
                    : $cmsData['galleryImages'];
                ?>
                

                    <!-- Select All Checkbox -->
                    <div class="mb-4 flex items-center">
                        <input type="checkbox"
                               id="selectAllImages"
                               class="w-4 h-4 text-salon-gold bg-secondary-600 border-secondary-500 rounded focus:ring-salon-gold focus:ring-2"
                               onchange="toggleSelectAll()">
                        <label for="selectAllImages" class="ml-2 text-white font-medium">Select All Images</label>
                        <span class="ml-4 text-gray-400 text-sm" id="selectionCount">0 selected</span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <?php if (empty($filteredImages)): ?>
                            <div class="col-span-full text-center py-12">
                                <div class="text-gray-400 text-lg mb-4">
                                    <i class="fas fa-images text-4xl mb-4"></i>
                                    <p>No gallery images found.</p>
                                    <p class="text-sm mt-2">Click "Add Gallery Image" to get started.</p>
                                </div>
                            </div>
                        <?php else: ?>
                            <?php foreach ($filteredImages as $image): ?>
                            <div class="bg-secondary-700 rounded-lg overflow-hidden relative">
                                <!-- Selection Checkbox -->
                                <div class="absolute top-2 left-2 z-10">
                                    <input type="checkbox"
                                           class="image-checkbox w-4 h-4 text-salon-gold bg-secondary-600 border-secondary-500 rounded focus:ring-salon-gold focus:ring-2"
                                           value="<?= htmlspecialchars($image['id']) ?>"
                                           onchange="updateBulkActions()">
                                </div>

                                <div class="aspect-square bg-gradient-to-br from-salon-gold/20 to-secondary-600 flex items-center justify-center">
                                    <?php if ($image['image_url']): ?>
                                        <img src="<?= htmlspecialchars($image['image_url']) ?>"
                                             alt="<?= htmlspecialchars($image['title']) ?>"
                                             class="w-full h-full object-cover"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="w-full h-full flex items-center justify-center text-center" style="display: none;">
                                            <div>
                                                <i class="fas fa-image text-4xl text-salon-gold mb-2"></i>
                                                <p class="text-gray-300 text-sm">Image Preview</p>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center">
                                            <i class="fas fa-image text-4xl text-salon-gold mb-2"></i>
                                            <p class="text-gray-300 text-sm">No Image</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-white truncate"><?= htmlspecialchars($image['title']) ?></h4>
                                        <button onclick="toggleImageStatus('<?= htmlspecialchars($image['id']) ?>', <?= $image['is_active'] ? 'false' : 'true' ?>)"
                                                class="text-xs px-2 py-1 rounded-full cursor-pointer transition-colors hover:opacity-80 <?= $image['is_active'] ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-red-100 text-red-800 hover:bg-red-200' ?>"
                                                title="Click to toggle status">
                                            <?= $image['is_active'] ? 'Active' : 'Hidden' ?>
                                        </button>
                                    </div>
                                    
                                    <p class="text-sm text-gray-400 mb-2"><?= htmlspecialchars($image['category']) ?></p>
                                    
                                    <?php if ($image['description']): ?>
                                        <p class="text-xs text-gray-500 mb-3"><?= htmlspecialchars(substr($image['description'], 0, 60)) ?>...</p>
                                    <?php endif; ?>
                                    
                                    <div class="flex flex-col space-y-2">
                                        <div class="flex flex-wrap gap-1">
                                            <button onclick="viewGalleryImage(<?= htmlspecialchars(json_encode($image, JSON_HEX_APOS | JSON_HEX_QUOT)) ?>)"
                                                    class="px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors font-medium shadow-sm" title="View Details">
                                                <i class="fas fa-eye mr-1"></i>View
                                            </button>
                                            <button onclick="editGalleryImage(<?= htmlspecialchars(json_encode($image, JSON_HEX_APOS | JSON_HEX_QUOT)) ?>)"
                                                    class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors font-medium shadow-sm" title="Edit">
                                                <i class="fas fa-edit mr-1"></i>Edit
                                            </button>
                                            <form method="POST" class="inline" onsubmit="return confirm('Are you sure?')">
                                                <input type="hidden" name="action" value="toggle_image_status">
                                                <input type="hidden" name="image_id" value="<?= $image['id'] ?>">
                                                <input type="hidden" name="tab" value="gallery">
                                                <button type="submit" class="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded-md transition-colors font-medium shadow-sm" title="Toggle Status">
                                                    <i class="fas fa-<?= $image['is_active'] ? 'eye-slash' : 'eye' ?> mr-1"></i><?= $image['is_active'] ? 'Hide' : 'Show' ?>
                                                </button>
                                            </form>
                                            <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this image?')">
                                                <input type="hidden" name="action" value="delete_gallery_image">
                                                <input type="hidden" name="image_id" value="<?= $image['id'] ?>">
                                                <input type="hidden" name="tab" value="gallery">
                                                <button type="submit" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors font-medium shadow-sm" title="Delete">
                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                </button>
                                            </form>
                                        </div>
                                        <div class="text-center">
                                            <span class="text-xs text-gray-500">
                                                <?= date('M j', strtotime($image['created_at'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
            </div>
        <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Content Modal -->
<div id="contentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="contentModalTitle" class="text-xl font-semibold text-white">Add Content</h3>
                    <button onclick="closeModal('contentModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="contentForm" method="POST" class="space-y-6">
                    <input type="hidden" id="contentAction" name="action" value="save_content">
                    <input type="hidden" id="contentId" name="content_id" value="">

                    <div>
                        <label for="contentSection" class="block text-sm font-medium text-gray-300 mb-2">Section *</label>
                        <select id="contentSection" name="section" required
                                class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">Select Section</option>
                            <?php foreach ($cmsData['contentTypes'] as $type => $label): ?>
                                <option value="<?= $type ?>"><?= $label ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div>
                        <label for="contentTitle" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                        <input type="text" id="contentTitle" name="title" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label for="contentText" class="block text-sm font-medium text-gray-300 mb-2">Content *</label>
                        <textarea id="contentText" name="content" rows="6" required
                                  class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                    </div>

                    <div>
                        <label for="contentImage" class="block text-sm font-medium text-gray-300 mb-2">Image URL (Optional)</label>
                        <input type="url" id="contentImage" name="image"
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="contentOrder" class="block text-sm font-medium text-gray-300 mb-2">Order</label>
                            <input type="number" id="contentOrder" name="order_index" min="1" value="1"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div class="flex items-end">
                            <label class="flex items-center">
                                <input type="checkbox" id="contentActive" name="is_active" checked
                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0">
                                <span class="ml-2 text-sm text-gray-300">Active</span>
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeModal('contentModal')"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            <span id="contentSubmitText">Save Content</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Gallery Modal -->
<div id="galleryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="galleryModalTitle" class="text-xl font-semibold text-white">Add Gallery Image</h3>
                    <button onclick="closeModal('galleryModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="galleryForm" method="POST" class="space-y-6">
                    <input type="hidden" id="galleryAction" name="action" value="add_gallery_image">
                    <input type="hidden" id="galleryImageId" name="image_id" value="">
                    <input type="hidden" name="tab" value="gallery">

                    <div>
                        <label for="galleryTitle" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                        <input type="text" id="galleryTitle" name="title" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label for="galleryDescription" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                        <textarea id="galleryDescription" name="description" rows="3"
                                  class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                    </div>

                    <div>
                        <label for="galleryImageUrl" class="block text-sm font-medium text-gray-300 mb-2">Image URL *</label>
                        <input type="url" id="galleryImageUrl" name="image_url" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label for="galleryCategory" class="block text-sm font-medium text-gray-300 mb-2">Category *</label>
                        <select id="galleryCategory" name="category" required
                                class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">Select Category</option>
                            <?php foreach ($cmsData['galleryCategories'] as $category): ?>
                                <option value="<?= htmlspecialchars($category) ?>"><?= htmlspecialchars($category) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="galleryOrder" class="block text-sm font-medium text-gray-300 mb-2">Order</label>
                            <input type="number" id="galleryOrder" name="order_index" min="0" value="0"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div class="flex items-end">
                            <label class="flex items-center">
                                <input type="checkbox" id="galleryActive" name="is_active" checked
                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0">
                                <span class="ml-2 text-sm text-gray-300">Active</span>
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeModal('galleryModal')"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            <span id="gallerySubmitText">Add Image</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- View Gallery Image Modal -->
<div id="viewGalleryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">Image Details</h2>
                    <button onclick="closeModal('viewGalleryModal')" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Image Preview -->
                    <div class="space-y-4">
                        <div class="aspect-square bg-gradient-to-br from-salon-gold/20 to-secondary-600 rounded-lg overflow-hidden">
                            <img id="viewImagePreview" src="" alt="" class="w-full h-full object-cover">
                        </div>
                        <div class="text-center">
                            <a id="viewImageLink" href="" target="_blank" class="text-salon-gold hover:text-yellow-400 text-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>Open in new tab
                            </a>
                        </div>
                    </div>

                    <!-- Image Information -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                            <p id="viewImageTitle" class="text-white bg-secondary-700 px-4 py-2 rounded-lg"></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                            <p id="viewImageDescription" class="text-white bg-secondary-700 px-4 py-2 rounded-lg min-h-[80px]"></p>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                                <p id="viewImageCategory" class="text-white bg-secondary-700 px-4 py-2 rounded-lg"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                                <p id="viewImageStatus" class="px-4 py-2 rounded-lg"></p>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Created</label>
                                <p id="viewImageCreated" class="text-white bg-secondary-700 px-4 py-2 rounded-lg"></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Updated</label>
                                <p id="viewImageUpdated" class="text-white bg-secondary-700 px-4 py-2 rounded-lg"></p>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Image URL</label>
                            <p id="viewImageUrl" class="text-salon-gold bg-secondary-700 px-4 py-2 rounded-lg break-all text-sm"></p>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700 mt-6">
                    <button type="button" onclick="closeModal('viewGalleryModal')"
                            class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                        Close
                    </button>
                    <button type="button" onclick="editFromView()"
                            class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                        <i class="fas fa-edit mr-2"></i>Edit Image
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openContentModal() {
    document.getElementById('contentModal').classList.remove('hidden');
    document.getElementById('contentModalTitle').textContent = 'Add Content';
    document.getElementById('contentSubmitText').textContent = 'Save Content';
    document.getElementById('contentAction').value = 'save_content';
    document.getElementById('contentId').value = '';
    document.getElementById('contentForm').reset();
    document.getElementById('contentActive').checked = true;
}

function openGalleryModal() {
    document.getElementById('galleryModal').classList.remove('hidden');
    document.getElementById('galleryModalTitle').textContent = 'Add Gallery Image';
    document.getElementById('gallerySubmitText').textContent = 'Add Image';
    document.getElementById('galleryAction').value = 'add_gallery_image';
    document.getElementById('galleryImageId').value = '';
    document.getElementById('galleryForm').reset();
    document.getElementById('galleryActive').checked = true;
}

function editContent(content) {
    document.getElementById('contentModal').classList.remove('hidden');
    document.getElementById('contentModalTitle').textContent = 'Edit Content';
    document.getElementById('contentSubmitText').textContent = 'Update Content';
    document.getElementById('contentAction').value = 'save_content';
    document.getElementById('contentId').value = content.id;

    document.getElementById('contentSection').value = content.section;
    document.getElementById('contentTitle').value = content.title;
    document.getElementById('contentText').value = content.content;
    document.getElementById('contentImage').value = content.image || '';
    document.getElementById('contentOrder').value = content.order_index;
    document.getElementById('contentActive').checked = content.is_active == 1;
}

function createContent(section) {
    openContentModal();
    document.getElementById('contentSection').value = section;
}

function editGalleryImage(image) {
    console.log('Edit button clicked', image);
    document.getElementById('galleryModal').classList.remove('hidden');
    document.getElementById('galleryModalTitle').textContent = 'Edit Gallery Image';
    document.getElementById('gallerySubmitText').textContent = 'Update Image';
    document.getElementById('galleryAction').value = 'update_gallery_image';
    document.getElementById('galleryImageId').value = image.id;

    document.getElementById('galleryTitle').value = image.title;
    document.getElementById('galleryDescription').value = image.description || '';
    document.getElementById('galleryImageUrl').value = image.image_url;
    document.getElementById('galleryCategory').value = image.category;
    document.getElementById('galleryOrder').value = image.order_index || 0;
    document.getElementById('galleryActive').checked = image.is_active == 1;
}

let currentViewImage = null;

function viewGalleryImage(image) {
    console.log('View button clicked', image);
    currentViewImage = image;

    document.getElementById('viewGalleryModal').classList.remove('hidden');
    document.getElementById('viewImagePreview').src = image.image_url;
    document.getElementById('viewImagePreview').alt = image.title;
    document.getElementById('viewImageLink').href = image.image_url;
    document.getElementById('viewImageTitle').textContent = image.title;
    document.getElementById('viewImageDescription').textContent = image.description || 'No description provided';
    document.getElementById('viewImageCategory').textContent = image.category;
    document.getElementById('viewImageUrl').textContent = image.image_url;

    // Format status
    const statusElement = document.getElementById('viewImageStatus');
    if (image.is_active == 1) {
        statusElement.textContent = 'Active';
        statusElement.className = 'px-4 py-2 rounded-lg bg-green-100 text-green-800';
    } else {
        statusElement.textContent = 'Hidden';
        statusElement.className = 'px-4 py-2 rounded-lg bg-red-100 text-red-800';
    }

    // Format dates
    const createdDate = new Date(image.created_at);
    const updatedDate = new Date(image.updated_at);
    document.getElementById('viewImageCreated').textContent = createdDate.toLocaleDateString() + ' ' + createdDate.toLocaleTimeString();
    document.getElementById('viewImageUpdated').textContent = updatedDate.toLocaleDateString() + ' ' + updatedDate.toLocaleTimeString();
}

function editFromView() {
    if (currentViewImage) {
        closeModal('viewGalleryModal');
        editGalleryImage(currentViewImage);
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        closeModal(e.target.id);
    }
});

// Bulk Actions Functions
function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.image-checkbox:checked');
    const bulkPanel = document.getElementById('bulkActionsPanel');
    const container = document.getElementById('selectedImagesContainer');
    const selectionCount = document.getElementById('selectionCount');
    const selectAllCheckbox = document.getElementById('selectAllImages');
    const allCheckboxes = document.querySelectorAll('.image-checkbox');

    // Update selection count
    if (selectionCount) {
        selectionCount.textContent = `${checkboxes.length} selected`;
    }

    // Update select all checkbox state
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        if (checkboxes.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else if (checkboxes.length > 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
    }

    // Clear existing hidden inputs
    container.innerHTML = '';

    if (checkboxes.length > 0) {
        bulkPanel.style.display = 'block';

        // Add hidden inputs for selected image IDs
        checkboxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'image_ids[]';
            input.value = checkbox.value;
            container.appendChild(input);
        });
    } else {
        bulkPanel.style.display = 'none';
    }
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllImages');
    const imageCheckboxes = document.querySelectorAll('.image-checkbox');

    imageCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActions();
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.image-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateBulkActions();
}

function confirmBulkAction() {
    const action = document.querySelector('select[name="bulk_action"]').value;
    const checkboxes = document.querySelectorAll('.image-checkbox:checked');

    if (!action) {
        alert('Please select an action');
        return false;
    }

    if (checkboxes.length === 0) {
        alert('Please select at least one image');
        return false;
    }

    let message = '';
    switch (action) {
        case 'activate':
            message = `Are you sure you want to activate ${checkboxes.length} selected image(s)?`;
            break;
        case 'deactivate':
            message = `Are you sure you want to deactivate ${checkboxes.length} selected image(s)?`;
            break;
        case 'delete':
            message = `Are you sure you want to delete ${checkboxes.length} selected image(s)? This action cannot be undone.`;
            break;
    }

    return confirm(message);
}

// Search functionality
function filterGallery() {
    const searchTerm = document.getElementById('gallerySearch').value.toLowerCase();
    const imageCards = document.querySelectorAll('.grid > div');
    let visibleCount = 0;

    imageCards.forEach(card => {
        const title = card.querySelector('h4')?.textContent.toLowerCase() || '';
        const category = card.querySelector('p')?.textContent.toLowerCase() || '';
        const description = card.querySelector('.text-xs')?.textContent.toLowerCase() || '';

        const matches = title.includes(searchTerm) ||
                       category.includes(searchTerm) ||
                       description.includes(searchTerm);

        if (matches) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // Update selection count and clear selections for hidden items
    const hiddenCheckboxes = document.querySelectorAll('.image-checkbox');
    hiddenCheckboxes.forEach(checkbox => {
        if (checkbox.closest('div').style.display === 'none') {
            checkbox.checked = false;
        }
    });

    updateBulkActions();
}

// Toggle single image status
function toggleImageStatus(imageId, newStatus) {
    if (confirm('Are you sure you want to change the status of this image?')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'toggle_image_status';

        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'image_id';
        idInput.value = imageId;

        const tabInput = document.createElement('input');
        tabInput.type = 'hidden';
        tabInput.name = 'tab';
        tabInput.value = 'gallery';

        form.appendChild(actionInput);
        form.appendChild(idInput);
        form.appendChild(tabInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// Image URL preview
document.getElementById('galleryImageUrl').addEventListener('input', function() {
    // Could add image preview functionality here
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
