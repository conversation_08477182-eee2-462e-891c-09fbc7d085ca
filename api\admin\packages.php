<?php
/**
 * Admin Packages API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/package_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetPackages();
            break;
            
        case 'POST':
            handleCreatePackage();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetPackages() {
    global $database;

    try {
        // Get all active packages
        $packages = $database->fetchAll("
            SELECT * FROM packages
            WHERE is_active = 1
            ORDER BY name
        ");
        
        // Format packages for API response
        $formattedPackages = [];
        foreach ($packages as $package) {
            $formattedPackages[] = [
                'id' => $package['id'],
                'name' => $package['name'],
                'description' => $package['description'],
                'price' => shouldShowPricing() ? (float)$package['price'] : 0,
                'image' => $package['image'],
                'is_active' => (bool)$package['is_active'],
                'created_at' => $package['created_at'],
                'updated_at' => $package['updated_at']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'packages' => $formattedPackages,
            'data' => $formattedPackages // Alternative key for compatibility
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch packages: ' . $e->getMessage()]);
    }
}

function handleCreatePackage() {
    try {
        // Get input data
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data']);
            return;
        }
        
        // Validate required fields
        $requiredFields = ['name', 'description', 'price', 'service_ids'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                http_response_code(400);
                echo json_encode(['error' => "Missing required field: $field"]);
                return;
            }
        }
        
        // Create package
        $packageData = [
            'name' => $input['name'],
            'description' => $input['description'],
            'price' => (float)$input['price'],
            'services' => $input['service_ids'],
            'is_active' => $input['status'] === 'ACTIVE' ? 1 : 0
        ];

        $result = createPackage($packageData);
        
        if ($result['success']) {
            echo json_encode([
                'success' => true,
                'message' => 'Package created successfully',
                'package_id' => $result['package_id']
            ]);
        } else {
            http_response_code(400);
            echo json_encode(['error' => $result['error']]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create package: ' . $e->getMessage()]);
    }
}
?>
