<?php
/**
 * Admin Services API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Set JSON header
header('Content-Type: application/json');

// Require admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path if present
$basePath = getBasePath();
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

$pathParts = explode('/', trim($path, '/'));

// Extract service ID if present
// Expected path: /api/admin/services/{id}
$serviceId = null;
if (count($pathParts) >= 4 && $pathParts[3] !== '') {
    $serviceId = $pathParts[3];
}

// Debug logging (remove in production)
error_log("Services API Debug - Original URI: " . $_SERVER['REQUEST_URI']);
error_log("Services API Debug - Processed path: " . $path);
error_log("Services API Debug - Path parts: " . print_r($pathParts, true));
error_log("Services API Debug - Service ID: " . ($serviceId ?? 'null'));

try {
    switch ($method) {
        case 'GET':
            if ($serviceId) {
                // Get single service
                $service = getServiceById($serviceId);
                if ($service) {
                    echo json_encode($service);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Service not found']);
                }
            } else {
                // Get all services with optional filters
                $page = (int)($_GET['page'] ?? 1);
                $limit = (int)($_GET['limit'] ?? 10);
                $search = $_GET['search'] ?? '';
                $category = $_GET['category'] ?? '';
                $active = $_GET['active'] ?? '';
                
                $offset = ($page - 1) * $limit;
                
                $whereClause = "WHERE 1=1";
                $params = [];
                
                if ($search) {
                    $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
                    $params[] = "%$search%";
                    $params[] = "%$search%";
                }
                
                if ($category) {
                    $whereClause .= " AND category = ?";
                    $params[] = $category;
                }
                
                if ($active !== '') {
                    $whereClause .= " AND is_active = ?";
                    $params[] = (int)$active;
                }
                
                $services = $database->fetchAll(
                    "SELECT * FROM services $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset",
                    $params
                );
                
                $total = $database->fetch(
                    "SELECT COUNT(*) as count FROM services $whereClause",
                    $params
                )['count'];
                
                echo json_encode([
                    'services' => $services,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $total,
                        'pages' => ceil($total / $limit)
                    ]
                ]);
            }
            break;
            
        case 'POST':
            // Create new service
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input']);
                break;
            }
            
            $result = createService($input);
            
            if ($result['success']) {
                http_response_code(201);
                echo json_encode(['success' => true, 'id' => $result['id']]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'PUT':
            // Update service
            if (!$serviceId) {
                http_response_code(400);
                echo json_encode(['error' => 'Service ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input']);
                break;
            }
            
            $result = updateService($serviceId, $input);
            
            if ($result['success']) {
                echo json_encode(['success' => true]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'DELETE':
            // Delete service
            if (!$serviceId) {
                http_response_code(400);
                echo json_encode(['error' => 'Service ID required']);
                break;
            }
            
            $result = deleteService($serviceId);
            
            if ($result['success']) {
                echo json_encode(['success' => true]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Services API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
