<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['service_id'])) {
                // Get variations for a specific service
                $serviceId = $_GET['service_id'];
                
                // Get service details
                $service = $database->fetch("SELECT * FROM services WHERE id = ? AND is_active = 1", [$serviceId]);
                if (!$service) {
                    http_response_code(404);
                    echo json_encode(['error' => 'Service not found']);
                    exit;
                }
                
                // Get active variations for the service
                $variations = getServiceVariations($serviceId, true); // true for active only
                
                // Format image URLs for public access
                if ($service['image'] && !filter_var($service['image'], FILTER_VALIDATE_URL)) {
                    $service['image'] = getBasePath() . '/uploads/' . $service['image'];
                }

                // Hide pricing for non-admin users
                if (!shouldShowPricing()) {
                    $service['price'] = 0;
                    foreach ($variations as &$variation) {
                        $variation['price'] = 0;
                    }
                }

                echo json_encode([
                    'success' => true,
                    'service' => $service,
                    'variations' => $variations,
                    'has_variations' => count($variations) > 0
                ]);
            } else {
                // Get all services with their variations (for public display)
                $services = getServicesWithVariations(true); // true for active only
                
                // Format image URLs for public access and hide pricing
                foreach ($services as &$service) {
                    if ($service['image'] && !filter_var($service['image'], FILTER_VALIDATE_URL)) {
                        $service['image'] = getBasePath() . '/uploads/' . $service['image'];
                    }

                    // Hide pricing for non-admin users
                    if (!shouldShowPricing()) {
                        $service['price'] = 0;
                        if (isset($service['variations'])) {
                            foreach ($service['variations'] as &$variation) {
                                $variation['price'] = 0;
                            }
                        }
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'services' => $services
                ]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("Customer service variations API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
