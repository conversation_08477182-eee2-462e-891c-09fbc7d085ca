<?php
/**
 * Public Services API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../config/app.php';

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path if present
$basePath = getBasePath();
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// Extract service ID from path if present
$pathParts = explode('/', trim($path, '/'));
$serviceId = null;

// Look for service ID in the path (e.g., /api/services/123)
if (count($pathParts) >= 3 && $pathParts[2] !== '') {
    $serviceId = $pathParts[2];
} elseif (isset($_GET['id'])) {
    $serviceId = $_GET['id'];
}

try {
    switch ($method) {
        case 'GET':
            if ($serviceId) {
                // Get single service
                $service = getServiceById($serviceId);
                if ($service && $service['is_active']) {
                    // Format image URL for public access
                    if ($service['image'] && !filter_var($service['image'], FILTER_VALIDATE_URL)) {
                        $service['image'] = getBasePath() . '/uploads/' . $service['image'];
                    }

                    // Hide pricing for non-admin users
                    if (!shouldShowPricing()) {
                        $service['price'] = 0;
                    }

                    echo json_encode($service);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Service not found']);
                }
            } else {
                // Get all active services with optional filters
                $search = $_GET['search'] ?? '';
                $category = $_GET['category'] ?? '';
                $minPrice = $_GET['min_price'] ?? '';
                $maxPrice = $_GET['max_price'] ?? '';
                $maxDuration = $_GET['max_duration'] ?? '';
                
                $whereClause = "WHERE is_active = 1";
                $params = [];
                
                if ($search) {
                    $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
                    $params[] = "%$search%";
                    $params[] = "%$search%";
                }
                
                if ($category) {
                    $whereClause .= " AND category = ?";
                    $params[] = $category;
                }
                
                if ($minPrice !== '') {
                    $whereClause .= " AND price >= ?";
                    $params[] = floatval($minPrice);
                }
                
                if ($maxPrice !== '') {
                    $whereClause .= " AND price <= ?";
                    $params[] = floatval($maxPrice);
                }
                
                if ($maxDuration !== '') {
                    $whereClause .= " AND duration <= ?";
                    $params[] = intval($maxDuration);
                }
                
                $services = $database->fetchAll(
                    "SELECT * FROM services $whereClause ORDER BY category, name",
                    $params
                );
                
                // Format image URLs for public access and hide pricing
                foreach ($services as &$service) {
                    if ($service['image'] && !filter_var($service['image'], FILTER_VALIDATE_URL)) {
                        $service['image'] = getBasePath() . '/uploads/' . $service['image'];
                    }

                    // Hide pricing for non-admin users
                    if (!shouldShowPricing()) {
                        $service['price'] = 0;
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'services' => $services,
                    'total' => count($services)
                ]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Public Services API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
