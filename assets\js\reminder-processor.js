/**
 * AJAX Reminder Background Processor
 * Silently processes reminders in the background without page reloads
 */

class ReminderProcessor {
    constructor(options = {}) {
        // Determine the correct base path for AJAX endpoints
        const basePath = this.getBasePath();

        this.options = {
            interval: options.interval || 180000, // 3 minutes default
            endpoint: options.endpoint || `${basePath}/ajax_process_reminders.php`,
            fallbackEndpoint: options.fallbackEndpoint || `${basePath}/ajax_process_reminders_simple.php`,
            showNotifications: options.showNotifications || false,
            isAdmin: options.isAdmin || false,
            debug: options.debug || false,
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 5000, // 5 seconds
            useFallback: false,
            ...options
        };
        
        this.isRunning = false;
        this.intervalId = null;
        this.retryCount = 0;
        this.lastProcessTime = null;
        this.stats = {
            totalProcessed: 0,
            totalSent: 0,
            totalFailed: 0,
            lastUpdate: null
        };
        
        this.init();
    }

    getBasePath() {
        // Get the current path and determine the base path to the root directory
        const currentPath = window.location.pathname;

        // If we're in admin, customer, or staff directory, go up one level
        if (currentPath.includes('/admin/') || currentPath.includes('/customer/') || currentPath.includes('/staff/')) {
            // Extract the base path (everything before /admin/, /customer/, or /staff/)
            const match = currentPath.match(/^(.*?)\/(admin|customer|staff)\//);
            if (match) {
                return match[1] || '';
            }
        }

        // If we're in the root directory or can't determine, use relative path
        const pathParts = currentPath.split('/');
        pathParts.pop(); // Remove the current file

        // If we're in a subdirectory, go up to root
        if (pathParts.length > 1 && (pathParts.includes('admin') || pathParts.includes('customer') || pathParts.includes('staff'))) {
            // Find the index of admin/customer/staff and take everything before it
            const adminIndex = pathParts.findIndex(part => ['admin', 'customer', 'staff'].includes(part));
            if (adminIndex > 0) {
                return pathParts.slice(0, adminIndex).join('/');
            }
        }

        // Default: assume we need to go up one level if we're in a subdirectory
        return currentPath.includes('/admin/') || currentPath.includes('/customer/') || currentPath.includes('/staff/') ? '..' : '.';
    }

    init() {
        this.log('Initializing reminder processor...');
        this.log(`Endpoint: ${this.options.endpoint}`);
        this.log(`Fallback endpoint: ${this.options.fallbackEndpoint}`);
        this.createStatusIndicator();
        this.start();
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.log('Page hidden, continuing background processing');
            } else {
                this.log('Page visible, resuming normal processing');
                // Process immediately when page becomes visible
                this.processReminders();
            }
        });
        
        // Handle before page unload
        window.addEventListener('beforeunload', () => {
            this.stop();
        });
    }
    
    start() {
        if (this.isRunning) {
            this.log('Processor already running');
            return;
        }
        
        this.isRunning = true;
        this.updateStatus('active', 'Reminder processor started');
        this.log('Starting reminder processor with ' + (this.options.interval / 1000) + 's interval');
        
        // Process immediately on start
        this.processReminders();
        
        // Set up interval
        this.intervalId = setInterval(() => {
            this.processReminders();
        }, this.options.interval);
    }
    
    stop() {
        if (!this.isRunning) {
            return;
        }
        
        this.isRunning = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        this.updateStatus('stopped', 'Reminder processor stopped');
        this.log('Reminder processor stopped');
    }
    
    async processReminders() {
        if (!this.isRunning) {
            return;
        }
        
        this.updateStatus('processing', 'Processing reminders...');
        this.log('Processing reminders...');
        
        try {
            const response = await this.makeRequest();
            
            if (response.success) {
                this.handleSuccess(response);
                this.retryCount = 0; // Reset retry count on success
            } else {
                this.handleError(new Error(response.message || 'Unknown error'));
            }
            
        } catch (error) {
            this.handleError(error);
        }
    }
    
    async makeRequest() {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 25000); // 25 second timeout

        const endpoint = this.options.useFallback ? this.options.fallbackEndpoint : this.options.endpoint;

        try {
            this.log(`Making request to: ${endpoint}`);

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    action: 'process_reminders',
                    timestamp: Date.now(),
                    fallback: this.options.useFallback
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                // Try fallback endpoint if main endpoint fails
                if (!this.options.useFallback && response.status >= 500) {
                    this.log('Main endpoint failed, trying fallback...');
                    this.options.useFallback = true;
                    return await this.makeRequest();
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            // Reset fallback flag on successful response
            if (this.options.useFallback && data.success) {
                this.log('Fallback endpoint successful, will retry main endpoint next time');
                this.options.useFallback = false;
            }

            return data;

        } catch (error) {
            clearTimeout(timeoutId);

            // Try fallback endpoint if main endpoint fails
            if (!this.options.useFallback && (error.name === 'AbortError' || error.message.includes('fetch'))) {
                this.log('Request failed, trying fallback endpoint...');
                this.options.useFallback = true;
                return await this.makeRequest();
            }

            throw error;
        }
    }
    
    handleSuccess(response) {
        this.lastProcessTime = new Date();
        this.stats.totalProcessed += response.data.processed || 0;
        this.stats.totalSent += response.data.sent || 0;
        this.stats.totalFailed += response.data.failed || 0;
        this.stats.lastUpdate = this.lastProcessTime;
        
        const message = response.message || 'Reminders processed successfully';
        this.updateStatus('success', message);
        this.log('Success: ' + message, response.data);
        
        // Show notification for admin users if enabled
        if (this.options.showNotifications && this.options.isAdmin) {
            const totalSent = (response.data.sent || 0) + (response.data.missed_recovered || 0) + (response.data.overdue_sent || 0);
            if (totalSent > 0) {
                this.showNotification(`${totalSent} reminder(s) sent successfully`, 'success');
            }
        }
        
        // Update status indicator with stats
        this.updateStatusIndicator(response.data);
    }
    
    handleError(error) {
        this.retryCount++;
        this.log('Error: ' + error.message);
        
        if (this.retryCount <= this.options.maxRetries) {
            this.updateStatus('retrying', `Retrying... (${this.retryCount}/${this.options.maxRetries})`);
            
            // Retry after delay
            setTimeout(() => {
                if (this.isRunning) {
                    this.processReminders();
                }
            }, this.options.retryDelay);
        } else {
            this.updateStatus('error', 'Failed to process reminders after ' + this.options.maxRetries + ' attempts');
            
            // Reset retry count and continue with normal interval
            this.retryCount = 0;
        }
    }
    
    createStatusIndicator() {
        if (!this.options.isAdmin) {
            return; // Only show indicator for admin users
        }
        
        // Create status indicator element
        const indicator = document.createElement('div');
        indicator.id = 'reminder-status-indicator';
        indicator.title = 'Reminder System Status\nFormat: Processed/Sent/Failed • Last Update Time';
        indicator.innerHTML = `
            <div class="reminder-status-content">
                <div class="status-icon">📧</div>
                <div class="status-text">Reminders</div>
                <div class="status-details">Initializing...</div>
            </div>
        `;
        
        // Add CSS styles
        const style = document.createElement('style');
        style.textContent = `
            #reminder-status-indicator {
                position: fixed;
                bottom: 15px;
                right: 15px;
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 10px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                font-size: 11px;
                z-index: 1000;
                min-width: 140px;
                max-width: 180px;
                transition: all 0.3s ease;
                backdrop-filter: blur(8px);
                opacity: 0.9;
            }

            #reminder-status-indicator:hover {
                opacity: 1;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            #reminder-status-indicator.active {
                border-color: #3b82f6;
                background: rgba(59, 130, 246, 0.08);
            }

            #reminder-status-indicator.processing {
                border-color: #f59e0b;
                background: rgba(245, 158, 11, 0.08);
            }

            #reminder-status-indicator.success {
                border-color: #10b981;
                background: rgba(16, 185, 129, 0.08);
            }

            #reminder-status-indicator.error {
                border-color: #ef4444;
                background: rgba(239, 68, 68, 0.08);
            }

            .reminder-status-content {
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .status-icon {
                font-size: 14px;
                flex-shrink: 0;
            }

            .status-text {
                font-weight: 600;
                color: #374151;
                font-size: 11px;
                line-height: 1.2;
                flex: 1;
                min-width: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .status-details {
                font-size: 9px;
                color: #6b7280;
                margin-top: 3px;
                line-height: 1.2;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            /* Hide on tablets and mobile devices */
            @media (max-width: 1024px) {
                #reminder-status-indicator {
                    display: none !important;
                }
            }

            /* Extra small screens - completely hidden */
            @media (max-width: 640px) {
                #reminder-status-indicator {
                    display: none !important;
                }
            }

            /* Desktop only - show smaller version */
            @media (min-width: 1025px) {
                #reminder-status-indicator {
                    display: block;
                }
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(indicator);
        
        this.statusIndicator = indicator;
    }
    
    updateStatus(status, message) {
        if (this.statusIndicator) {
            this.statusIndicator.className = status;
            const statusText = this.statusIndicator.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = message;
            }
        }
    }
    
    updateStatusIndicator(data) {
        if (!this.statusIndicator) return;

        const details = this.statusIndicator.querySelector('.status-details');
        if (details && data) {
            const totalSent = (data.sent || 0) + (data.missed_recovered || 0) + (data.overdue_sent || 0);
            const lastTime = this.lastProcessTime ? this.lastProcessTime.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            }) : 'Never';

            details.textContent = `${data.processed || 0}/${totalSent}/${data.failed || 0} • ${lastTime}`;
        }
    }
    
    showNotification(message, type = 'info') {
        // Create temporary notification
        const notification = document.createElement('div');
        notification.className = `reminder-notification ${type}`;
        notification.textContent = message;
        
        // Add notification styles
        const style = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            font-size: 14px;
            max-width: 300px;
            animation: slideIn 0.3s ease;
        `;
        
        notification.style.cssText = style;
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    log(message, data = null) {
        if (this.options.debug) {
            console.log('[ReminderProcessor]', message, data || '');
        }
    }
    
    getStats() {
        return {
            ...this.stats,
            isRunning: this.isRunning,
            retryCount: this.retryCount,
            lastProcessTime: this.lastProcessTime
        };
    }
}

// Auto-initialize based on page context
document.addEventListener('DOMContentLoaded', function() {
    // Check if automatic reminder processing is disabled (for production with cron jobs)
    // This will be set by PHP in the page header
    if (window.DISABLE_AUTO_REMINDER_PROCESSING === true) {
        console.log('Automatic reminder processing disabled - using cron job instead');
        return;
    }

    // Detect if this is an admin page
    const isAdmin = document.body.classList.contains('admin-page') ||
                   window.location.pathname.includes('/admin/') ||
                   document.querySelector('.admin-sidebar') !== null;

    // Initialize reminder processor
    window.reminderProcessor = new ReminderProcessor({
        interval: 120000, // 2 minutes
        showNotifications: isAdmin, // Only show notifications for admin
        isAdmin: isAdmin,
        debug: true // Temporarily enabled for debugging
    });

    console.log('Reminder processor initialized for', isAdmin ? 'admin' : 'customer', 'page');
    console.log('Processor endpoints:', {
        main: window.reminderProcessor.options.endpoint,
        fallback: window.reminderProcessor.options.fallbackEndpoint
    });
});
