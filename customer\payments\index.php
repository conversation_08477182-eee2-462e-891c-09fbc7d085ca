<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Fix currency symbol if corrupted in database
try {
    $currentSymbol = getSetting('business', 'currency_symbol', null);
    if ($currentSymbol === '262145' || $currentSymbol === 262145) {
        setSetting('business', 'currency_symbol', 'TSH');
        error_log("Customer payments: Fixed corrupted currency_symbol (was 262145, now TSH)");
    }
} catch (Exception $e) {
    error_log("Customer payments: Error checking currency_symbol: " . $e->getMessage());
}
require_once __DIR__ . '/../../includes/booking_expiration.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Check if payments are enabled
if (!PAYMENT_ENABLED) {
    redirect('/customer/bookings');
}

$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);

// Get eligible bookings for payment
$eligibleBookings = getEligibleBookingsForPayment($customerId);

// Debug: Log the bookings data
error_log('Eligible bookings count: ' . count($eligibleBookings));
if (!empty($eligibleBookings)) {
    error_log('First booking data: ' . json_encode($eligibleBookings[0]));
}

$pageTitle = "Payment Center";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Page Header -->
<div class="bg-secondary-800 shadow mb-8">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="py-6 md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div>
                        <div class="flex items-center">
                            <h1 class="text-2xl font-bold leading-7 text-white sm:leading-9 sm:truncate">
                                Payment Center
                            </h1>
                        </div>
                        <dl class="mt-2 flex flex-col sm:mt-1 sm:flex-row sm:flex-wrap">
                            <dt class="sr-only">Page description</dt>
                            <dd class="flex items-center text-sm text-gray-300 font-medium sm:mr-6">
                                Pay for your confirmed appointments securely
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="mt-6 flex space-x-3 md:mt-0 md:ml-4">
                <a href="<?= getBasePath() ?>/customer/bookings" class="inline-flex items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-white bg-secondary-700 hover:bg-secondary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Bookings
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Payment Gateway Status -->
<div class="mb-6">
    <div class="bg-secondary-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Available Payment Methods</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <?php if (DPO_ENABLED): ?>
            <div class="flex items-center p-4 bg-secondary-700 rounded-lg">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-salon-gold" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <h4 class="text-white font-medium">DPO Pay</h4>
                    <p class="text-gray-400 text-sm">Cards, Mobile Money, Bank Transfer</p>
                </div>
                <div class="ml-auto">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Primary
                    </span>
                </div>
            </div>
            <?php endif; ?>

            <?php if (STRIPE_ENABLED): ?>
            <div class="flex items-center p-4 bg-secondary-700 rounded-lg opacity-50">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.90-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <h4 class="text-gray-500 font-medium">Stripe</h4>
                    <p class="text-gray-500 text-sm">Not available in Tanzania</p>
                </div>
                <div class="ml-auto">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Disabled
                    </span>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (FLUTTERWAVE_ENABLED): ?>
            <div class="flex items-center p-4 bg-secondary-700 rounded-lg">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8.64 5.23L7.2 6.67l4.8 4.8-4.8 4.8 1.44 1.44L14.08 12 8.64 5.23z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <h4 class="text-white font-medium">Flutterwave</h4>
                    <p class="text-gray-400 text-sm">Cards, Bank Transfer, Mobile Money</p>
                </div>
                <div class="ml-auto">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Available
                    </span>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Debug Information (remove in production) -->
<?php if (isset($_GET['debug'])): ?>
<div class="bg-red-800 rounded-lg p-4 mb-6">
    <h3 class="text-white font-semibold mb-2">Debug Information</h3>
    <pre class="text-white text-xs overflow-auto">
Customer ID: <?= $customerId ?>
Eligible Bookings Count: <?= count($eligibleBookings) ?>
<?php if (!empty($eligibleBookings)): ?>
First Booking Data:
<?= json_encode($eligibleBookings[0], JSON_PRETTY_PRINT) ?>

Test Payment Modal:
    </pre>
    <button onclick="directPaymentModal('<?= addslashes($eligibleBookings[0]['id']) ?>', <?= intval($eligibleBookings[0]['total_amount']) ?>, '<?= addslashes($eligibleBookings[0]['service_name']) ?>')"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mt-2 mr-2">
        Test Direct Payment
    </button>
    <button onclick="testAPIInput('<?= addslashes($eligibleBookings[0]['id']) ?>', <?= intval($eligibleBookings[0]['total_amount']) ?>)"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded mt-2 mr-2">
        Test API Input
    </button>
    <button onclick="debugBooking('<?= addslashes($eligibleBookings[0]['id']) ?>')"
            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded mt-2 mr-2">
        Debug Booking
    </button>
    <button onclick="showCurrentValues()"
            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded mt-2 mr-2">
        Show Current Values
    </button>
    <button onclick="inspectPaymentButtons()"
            class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded mt-2 mr-2">
        Inspect Payment Buttons
    </button>
    <button onclick="showPaymentTrace()"
            class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded mt-2">
        Show Payment Trace
    </button>
    <pre class="text-white text-xs">
<?php endif; ?>
    </pre>
</div>
<?php endif; ?>

<!-- Eligible Bookings -->
<div class="space-y-4">
    <?php if (empty($eligibleBookings)): ?>
        <div class="bg-secondary-800 rounded-lg p-12 text-center">
            <i class="fas fa-credit-card text-6xl text-gray-600 mb-4"></i>
            <h3 class="text-xl font-semibold text-white mb-2">No Payments Required</h3>
            <p class="text-gray-400 mb-6">You don't have any confirmed bookings that require payment at this time.</p>
            <a href="<?= getBasePath() ?>/customer/bookings" class="bg-salon-gold hover:bg-yellow-500 text-black px-6 py-3 rounded-lg font-semibold transition-colors">
                <i class="fas fa-calendar mr-2"></i>View All Bookings
            </a>
        </div>
    <?php else: ?>
        <div class="bg-secondary-800 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-white mb-4">Bookings Ready for Payment</h3>
            <p class="text-gray-400 text-sm">
                The following confirmed bookings are ready for payment. Payment is optional but helps secure your appointment.
            </p>
        </div>
        
        <?php foreach ($eligibleBookings as $booking): ?>
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex-1">
                        <div class="flex items-start gap-4">
                            <div class="w-12 h-12 rounded-full bg-salon-gold flex items-center justify-center">
                                <?php if (!empty($booking['package_name'])): ?>
                                    <i class="fas fa-box text-black"></i>
                                <?php else: ?>
                                    <i class="fas fa-cut text-black"></i>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <h3 class="text-lg font-semibold text-white">
                                        <?php if (!empty($booking['service_name'])): ?>
                                            <?= htmlspecialchars($booking['service_name']) ?>
                                        <?php elseif (!empty($booking['package_name'])): ?>
                                            <?= htmlspecialchars($booking['package_name']) ?>
                                            <span class="text-xs bg-salon-gold text-black px-2 py-1 rounded ml-2">PACKAGE</span>
                                        <?php else: ?>
                                            Unknown Service
                                        <?php endif; ?>
                                    </h3>
                                    <?php $statusInfo = getBookingStatusInfo($booking['status']); ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusInfo['class'] ?>">
                                        <i class="<?= $statusInfo['icon'] ?> mr-1"></i>
                                        <?= $statusInfo['label'] ?>
                                    </span>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <p class="text-gray-400">Staff Member</p>
                                        <p class="text-white font-medium"><?= htmlspecialchars($booking['staff_name']) ?></p>
                                    </div>
                                    <div>
                                        <p class="text-gray-400">Date & Time</p>
                                        <p class="text-white font-medium">
                                            <?= date('M j, Y', strtotime($booking['date'])) ?><br>
                                            <?= date('g:i A', strtotime($booking['start_time'])) ?>
                                        </p>
                                    </div>
                                    <?php if (shouldShowPricing()): ?>
                                        <div>
                                            <p class="text-gray-400">Amount</p>
                                            <p class="text-white font-medium text-lg">
                                                <?= formatCurrency($booking['total_amount'], null, true) ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($booking['payment_status']): ?>
                                    <div class="mt-3">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            <?= $booking['payment_status'] === 'COMPLETED' ? 'bg-green-100 text-green-800' : 
                                                ($booking['payment_status'] === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') ?>">
                                            Payment: <?= ucfirst(strtolower($booking['payment_status'])) ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 lg:mt-0 lg:ml-6">
                        <?php if (!$booking['payment_status'] || $booking['payment_status'] === 'FAILED'): ?>
                            <button class="w-full lg:w-auto px-6 py-3 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors"
                                    onclick="directPaymentModal('<?= addslashes($booking['id']) ?>', <?= intval($booking['total_amount']) ?>, '<?= addslashes($booking['service_name'] ?: $booking['package_name']) ?>')">
                                <i class="fas fa-credit-card mr-2"></i>Pay Now
                            </button>
                        <?php elseif ($booking['payment_status'] === 'PENDING'): ?>
                            <div class="text-center">
                                <span class="px-4 py-2 bg-yellow-600 text-white rounded-lg">
                                    <i class="fas fa-clock mr-2"></i>Payment Processing
                                </span>
                                <button onclick="verifyPayment('<?= $booking['payment_id'] ?>')" 
                                        class="mt-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors">
                                    <i class="fas fa-sync mr-1"></i>Check Status
                                </button>
                            </div>
                        <?php else: ?>
                            <span class="px-4 py-2 bg-green-600 text-white rounded-lg">
                                <i class="fas fa-check mr-2"></i>Paid
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Payment Modal -->
<div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Choose Payment Method</h3>
                    <button onclick="closePaymentModal()" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div id="paymentDetails" class="mb-6">
                    <!-- Payment details will be populated by JavaScript -->
                </div>

                <div class="space-y-3">
                    <?php if (DPO_ENABLED): ?>
                    <button onclick="processPayment('dpo')"
                            class="w-full flex items-center justify-center px-4 py-3 bg-salon-gold hover:bg-yellow-600 text-black rounded-lg font-medium transition-colors">
                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        Pay with DPO Pay
                    </button>
                    <?php endif; ?>

                    <?php if (FLUTTERWAVE_ENABLED): ?>
                    <button onclick="processPayment('flutterwave')"
                            class="w-full flex items-center justify-center px-4 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors">
                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8.64 5.23L7.2 6.67l4.8 4.8-4.8 4.8 1.44 1.44L14.08 12 8.64 5.23z"/>
                        </svg>
                        Pay with Flutterwave
                    </button>
                    <?php endif; ?>

                    <?php if (STRIPE_ENABLED): ?>
                    <button disabled
                            class="w-full flex items-center justify-center px-4 py-3 bg-gray-600 text-gray-400 rounded-lg font-medium cursor-not-allowed opacity-50">
                        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z"/>
                        </svg>
                        Stripe (Not Available)
                    </button>
                    <?php endif; ?>
                </div>

                <div class="mt-6 text-center">
                    <button onclick="cancelPayment()"
                            class="px-4 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-secondary-800 rounded-lg p-8 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-salon-gold mx-auto mb-4"></div>
            <p class="text-white">Processing payment...</p>
        </div>
    </div>
</div>

<script>
// Global variables with detailed tracking
let currentBookingId = null;
let currentAmount = null;
let currentServiceName = null;

// Debug tracking object
window.paymentDebug = {
    trace: [],
    log: function(step, data) {
        const timestamp = new Date().toISOString();
        const entry = { timestamp, step, data: JSON.parse(JSON.stringify(data)) };
        this.trace.push(entry);
        console.log(`[PAYMENT TRACE ${timestamp}] ${step}:`, data);
    },
    getTrace: function() {
        return this.trace;
    },
    getCurrentState: function() {
        return {
            currentBookingId: currentBookingId,
            currentAmount: currentAmount,
            currentServiceName: currentServiceName,
            timestamp: new Date().toISOString()
        };
    }
};

// Log initial state
window.paymentDebug.log('INITIAL_STATE', window.paymentDebug.getCurrentState());

// SIMPLE, DIRECT APPROACH - No data attributes, no complex logic
function directPaymentModal(bookingId, amount, serviceName) {
    window.paymentDebug.log('DIRECT_PAYMENT_MODAL_START', {
        bookingId: bookingId,
        amount: amount,
        serviceName: serviceName,
        bookingIdType: typeof bookingId,
        amountType: typeof amount,
        serviceNameType: typeof serviceName,
        currentState: window.paymentDebug.getCurrentState()
    });

    // Validate parameters
    if (!bookingId || bookingId === '' || bookingId === 'null' || bookingId === 'undefined') {
        window.paymentDebug.log('VALIDATION_ERROR_BOOKING_ID', { bookingId: bookingId });
        console.error('CRITICAL: Invalid booking ID received:', bookingId);
        alert('Error: Invalid booking ID. Please refresh the page and try again.');
        return;
    }

    if (!amount || amount <= 0 || isNaN(amount)) {
        window.paymentDebug.log('VALIDATION_ERROR_AMOUNT', { amount: amount });
        console.error('CRITICAL: Invalid amount received:', amount);
        alert('Error: Invalid amount. Please refresh the page and try again.');
        return;
    }

    // Set global variables directly
    const oldState = window.paymentDebug.getCurrentState();

    currentBookingId = String(bookingId).trim();
    currentAmount = parseInt(amount);
    currentServiceName = serviceName || 'Unknown Service';

    // Store in backup system
    window.paymentBackup.store(currentBookingId, currentAmount, currentServiceName);

    const newState = window.paymentDebug.getCurrentState();

    window.paymentDebug.log('GLOBAL_VARIABLES_SET', {
        oldState: oldState,
        newState: newState,
        setValues: {
            currentBookingId: currentBookingId,
            currentAmount: currentAmount,
            currentServiceName: currentServiceName
        },
        backupStored: window.paymentBackup.bookingData
    });

    // Call the existing modal function
    openPaymentModal(currentBookingId, currentAmount, currentServiceName);
}

function handlePaymentClick(clickedElement) {
    console.log('=== handlePaymentClick CALLED ===');
    console.log('Clicked element:', clickedElement);
    console.log('Clicked element HTML:', clickedElement.outerHTML);

    // Find the actual button element (in case we clicked on a child element like an icon)
    let button = clickedElement;

    // If the clicked element doesn't have data-booking-id, look for parent with it
    if (!button.hasAttribute('data-booking-id')) {
        button = clickedElement.closest('[data-booking-id]');
        console.log('Found parent button with data attributes:', button);
    }

    if (!button) {
        console.error('CRITICAL: Could not find button with data-booking-id');
        alert('Error: Could not find payment button data. Please refresh the page.');
        return;
    }

    console.log('Using button element:', button);
    console.log('Button HTML:', button.outerHTML);

    // Get data from button attributes with multiple fallback methods
    let bookingId = button.getAttribute('data-booking-id') ||
                   button.dataset.bookingId ||
                   button.getAttribute('data-debug-booking-id');

    let amount = button.getAttribute('data-amount') ||
                button.dataset.amount;

    let serviceName = button.getAttribute('data-service-name') ||
                     button.dataset.serviceName;

    console.log('Raw attribute values (multiple methods):', {
        'getAttribute data-booking-id': button.getAttribute('data-booking-id'),
        'dataset.bookingId': button.dataset.bookingId,
        'getAttribute data-amount': button.getAttribute('data-amount'),
        'dataset.amount': button.dataset.amount,
        'getAttribute data-service-name': button.getAttribute('data-service-name'),
        'dataset.serviceName': button.dataset.serviceName,
        'all attributes': Array.from(button.attributes).map(attr => attr.name + '=' + attr.value)
    });

    console.log('Final extracted data:', {
        bookingId: bookingId,
        amount: amount,
        serviceName: serviceName,
        bookingIdType: typeof bookingId,
        amountType: typeof amount,
        bookingIdLength: bookingId ? bookingId.length : 0
    });

    // Validate data and use fallback if needed
    if (!bookingId || bookingId.trim() === '') {
        console.warn('No booking ID found in button attributes, trying fallback method...');

        // Fallback: Use global booking data if available
        if (window.bookingData && window.bookingData.length > 0) {
            const firstBooking = window.bookingData[0];
            bookingId = firstBooking.id;
            amount = firstBooking.total_amount;
            serviceName = firstBooking.service_name || firstBooking.package_name;

            console.log('Using fallback booking data:', {
                bookingId: bookingId,
                amount: amount,
                serviceName: serviceName
            });
        } else {
            console.error('CRITICAL: No booking ID found and no fallback data available');
            alert('Error: Booking ID is missing. Please refresh the page and try again.');
            return;
        }
    }

    if (!amount || amount === '0') {
        console.error('CRITICAL: No amount found even after fallback');
        alert('Error: Amount is missing. Please refresh the page and try again.');
        return;
    }

    // Ensure amount is a number
    const numericAmount = parseInt(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
        console.error('CRITICAL: Invalid amount after parsing:', numericAmount);
        alert('Error: Invalid amount. Please refresh the page and try again.');
        return;
    }

    console.log('Final data being passed to openPaymentModal:', {
        bookingId: bookingId,
        amount: numericAmount,
        serviceName: serviceName
    });

    // Call the modal function with the extracted data
    openPaymentModal(bookingId, numericAmount, serviceName);
}

function testPaymentModal(bookingId, amount, serviceName) {
    console.log('Test payment modal called with:', {
        bookingId: bookingId,
        amount: amount,
        serviceName: serviceName
    });

    alert('Test Data:\nBooking ID: ' + bookingId + '\nAmount: ' + amount + '\nService: ' + serviceName);
    openPaymentModal(bookingId, amount, serviceName);
}

function showCurrentValues() {
    const message = 'Current JavaScript Values:\n\n' +
                   'currentBookingId: ' + currentBookingId + ' (type: ' + typeof currentBookingId + ')\n' +
                   'currentAmount: ' + currentAmount + ' (type: ' + typeof currentAmount + ')\n' +
                   'currentServiceName: ' + currentServiceName + ' (type: ' + typeof currentServiceName + ')\n\n' +
                   'Null checks:\n' +
                   'bookingId === null: ' + (currentBookingId === null) + '\n' +
                   'bookingId === undefined: ' + (currentBookingId === undefined) + '\n' +
                   'amount === null: ' + (currentAmount === null) + '\n' +
                   'amount === undefined: ' + (currentAmount === undefined);

    console.log('Current values:', {
        currentBookingId: currentBookingId,
        currentAmount: currentAmount,
        currentServiceName: currentServiceName
    });

    alert(message);
}

function inspectPaymentButtons() {
    const paymentButtons = document.querySelectorAll('.payment-btn, [data-booking-id]');
    console.log('Found payment buttons:', paymentButtons.length);

    let message = 'Payment Buttons Inspection:\n\n';
    message += 'Found ' + paymentButtons.length + ' payment buttons\n\n';

    paymentButtons.forEach((button, index) => {
        const bookingId = button.getAttribute('data-booking-id');
        const amount = button.getAttribute('data-amount');
        const serviceName = button.getAttribute('data-service-name');
        const onclick = button.getAttribute('onclick');

        console.log('Button ' + index + ':', {
            element: button,
            bookingId: bookingId,
            amount: amount,
            serviceName: serviceName,
            onclick: onclick,
            outerHTML: button.outerHTML
        });

        message += 'Button ' + (index + 1) + ':\n';
        message += '  Booking ID: ' + bookingId + '\n';
        message += '  Amount: ' + amount + '\n';
        message += '  Service: ' + serviceName + '\n';
        message += '  OnClick: ' + onclick + '\n\n';
    });

    alert(message);
}

function showPaymentTrace() {
    const trace = window.paymentDebug.getTrace();
    const currentState = window.paymentDebug.getCurrentState();

    console.log('=== COMPLETE PAYMENT TRACE ===');
    console.log('Current State:', currentState);
    console.log('Full Trace:', trace);

    let message = 'Payment System Trace:\n\n';
    message += 'Current State:\n';
    message += `  Booking ID: ${currentState.currentBookingId}\n`;
    message += `  Amount: ${currentState.currentAmount}\n`;
    message += `  Service: ${currentState.currentServiceName}\n\n`;

    message += 'Trace Steps:\n';
    trace.forEach((entry, index) => {
        message += `${index + 1}. ${entry.step} (${entry.timestamp})\n`;
    });

    alert(message);
}

// BULLETPROOF BACKUP SYSTEM
window.paymentBackup = {
    bookingData: null,

    store: function(bookingId, amount, serviceName) {
        this.bookingData = {
            bookingId: String(bookingId),
            amount: parseInt(amount),
            serviceName: serviceName,
            timestamp: new Date().toISOString()
        };
        window.paymentDebug.log('BACKUP_STORED', this.bookingData);

        // Also store in localStorage as ultimate backup
        try {
            localStorage.setItem('paymentBackup', JSON.stringify(this.bookingData));
        } catch (e) {
            console.warn('Could not store backup in localStorage:', e);
        }
    },

    restore: function() {
        if (this.bookingData) {
            window.paymentDebug.log('BACKUP_RESTORED', this.bookingData);
            return this.bookingData;
        }

        // Try localStorage backup
        try {
            const stored = localStorage.getItem('paymentBackup');
            if (stored) {
                this.bookingData = JSON.parse(stored);
                window.paymentDebug.log('BACKUP_RESTORED_FROM_LOCALSTORAGE', this.bookingData);
                return this.bookingData;
            }
        } catch (e) {
            console.warn('Could not restore from localStorage:', e);
        }

        return null;
    },

    clear: function() {
        this.bookingData = null;
        try {
            localStorage.removeItem('paymentBackup');
        } catch (e) {
            console.warn('Could not clear localStorage backup:', e);
        }
        window.paymentDebug.log('BACKUP_CLEARED', {});
    }
};

async function testAPIInput(bookingId, amount) {
    console.log('Testing API input with:', { bookingId, amount });

    try {
        const testData = {
            booking_id: bookingId,
            amount: parseInt(amount),
            gateway: 'STRIPE'
        };

        console.log('Sending test data:', testData);

        const response = await fetch('<?= getBasePath() ?>/api/payments/test-input.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        const result = await response.json();
        console.log('Test API Response:', result);

        // Show results in a more readable format
        const debugInfo = result.debug_info;
        const extractedData = result.extracted_data;

        let message = 'API Input Test Results:\n\n';
        message += 'Raw Input Length: ' + debugInfo.raw_input_length + '\n';
        message += 'JSON Parse Error: ' + debugInfo.json_last_error + '\n\n';

        if (extractedData) {
            message += 'Extracted Data:\n';
            message += 'Booking ID: "' + extractedData.booking_id + '" (length: ' + extractedData.booking_id_length + ')\n';
            message += 'Amount: ' + extractedData.amount + ' (type: ' + extractedData.amount_type + ')\n';
            message += 'Gateway: ' + extractedData.gateway + '\n';
            message += 'Booking ID Empty: ' + extractedData.booking_id_empty + '\n';
        }

        alert(message);

    } catch (error) {
        console.error('Test API error:', error);
        alert('Test API Error: ' + error.message);
    }
}

async function debugBooking(bookingId) {
    console.log('Debugging booking:', bookingId);

    try {
        const response = await fetch('<?= getBasePath() ?>/api/payments/debug-booking.php?booking_id=' + encodeURIComponent(bookingId));
        const result = await response.json();

        console.log('Booking Debug Response:', result);

        if (result.success) {
            const debug = result.debug_info;
            let message = 'Booking Debug Results:\n\n';
            message += 'Session User ID: ' + debug.session_user_id + '\n';
            message += 'Requested Booking ID: ' + debug.requested_booking_id + '\n';
            message += 'Booking Found: ' + debug.booking_found + '\n\n';

            if (debug.booking_data) {
                message += 'Booking Data:\n';
                message += 'User ID: ' + debug.booking_data.user_id + '\n';
                message += 'Status: ' + debug.booking_data.status + '\n';
                message += 'Amount: ' + debug.booking_data.total_amount + '\n\n';

                if (debug.booking_checks) {
                    message += 'Checks:\n';
                    message += 'User ID Match: ' + debug.booking_checks.user_id_match + '\n';
                    message += 'Status Eligible: ' + debug.booking_checks.status_eligible + '\n';
                    message += 'Has Existing Payment: ' + debug.booking_checks.has_existing_payment + '\n';
                    message += 'Payment Status: ' + debug.booking_checks.payment_status + '\n';
                }
            }

            message += '\nEligible Bookings Count: ' + debug.eligible_bookings_count;

            alert(message);
        } else {
            alert('Debug Error: ' + result.error);
        }

    } catch (error) {
        console.error('Debug booking error:', error);
        alert('Debug Error: ' + error.message);
    }
}

function openPaymentModal(bookingId, amount, serviceName) {
    window.paymentDebug.log('OPEN_PAYMENT_MODAL_START', {
        parameters: {
            bookingId: bookingId,
            amount: amount,
            serviceName: serviceName,
            bookingIdType: typeof bookingId,
            amountType: typeof amount
        },
        currentState: window.paymentDebug.getCurrentState()
    });

    // Simple validation - data should already be validated by directPaymentModal
    if (!bookingId || !amount) {
        window.paymentDebug.log('OPEN_MODAL_VALIDATION_ERROR', {
            bookingId: bookingId,
            amount: amount
        });
        console.error('Missing required parameters');
        alert('Error: Missing payment data. Please try again.');
        return;
    }

    // Set global variables (should already be set by directPaymentModal, but just in case)
    const oldState = window.paymentDebug.getCurrentState();

    currentBookingId = String(bookingId);
    currentAmount = parseInt(amount);
    currentServiceName = serviceName || 'Unknown Service';

    const newState = window.paymentDebug.getCurrentState();

    window.paymentDebug.log('OPEN_MODAL_VARIABLES_SET', {
        oldState: oldState,
        newState: newState,
        parameters: {
            bookingId: bookingId,
            amount: amount,
            serviceName: serviceName
        }
    });

    // Update modal content
    const modalContent = `
        <div class="bg-secondary-700 rounded-lg p-4">
            <h4 class="text-white font-medium mb-2">${currentServiceName}</h4>
            <p class="text-gray-400 text-sm mb-2">Booking ID: ${currentBookingId}</p>
            <?php if (shouldShowPricing()): ?>
                <p class="text-salon-gold text-lg font-semibold"><?= CURRENCY_SYMBOL ?> ${currentAmount.toLocaleString()}</p>
            <?php else: ?>
                <p class="text-salon-gold text-lg font-semibold">Contact for pricing</p>
            <?php endif; ?>
        </div>
    `;

    document.getElementById('paymentDetails').innerHTML = modalContent;

    window.paymentDebug.log('MODAL_CONTENT_SET', {
        modalContent: modalContent,
        currentState: window.paymentDebug.getCurrentState()
    });

    document.getElementById('paymentModal').classList.remove('hidden');

    window.paymentDebug.log('MODAL_DISPLAYED', {
        currentState: window.paymentDebug.getCurrentState()
    });
}

function closePaymentModal() {
    window.paymentDebug.log('CLOSE_PAYMENT_MODAL_CALLED', {
        currentState: window.paymentDebug.getCurrentState(),
        stackTrace: new Error().stack
    });

    document.getElementById('paymentModal').classList.add('hidden');

    // DON'T reset the variables here - they're needed for payment processing!
    // Variables will be reset after successful payment or explicit cancellation
    window.paymentDebug.log('MODAL_CLOSED_VARIABLES_PRESERVED', {
        currentState: window.paymentDebug.getCurrentState()
    });
}

function resetPaymentVariables() {
    window.paymentDebug.log('RESET_PAYMENT_VARIABLES_CALLED', {
        oldState: window.paymentDebug.getCurrentState()
    });

    currentBookingId = null;
    currentAmount = null;
    currentServiceName = null;

    // Clear backup as well
    window.paymentBackup.clear();

    window.paymentDebug.log('PAYMENT_VARIABLES_RESET', {
        newState: window.paymentDebug.getCurrentState()
    });
}

function cancelPayment() {
    window.paymentDebug.log('CANCEL_PAYMENT_CALLED', {
        currentState: window.paymentDebug.getCurrentState()
    });

    closePaymentModal();
    resetPaymentVariables();
}

function showLoading() {
    document.getElementById('loadingModal').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loadingModal').classList.add('hidden');
}

async function processPayment(gateway) {
    window.paymentDebug.log('PROCESS_PAYMENT_START', {
        gateway: gateway,
        currentState: window.paymentDebug.getCurrentState(),
        globalVariables: {
            currentBookingId: currentBookingId,
            currentAmount: currentAmount,
            currentServiceName: currentServiceName,
            types: {
                bookingIdType: typeof currentBookingId,
                amountType: typeof currentAmount,
                serviceNameType: typeof currentServiceName
            },
            nullChecks: {
                bookingIdIsNull: currentBookingId === null,
                bookingIdIsUndefined: currentBookingId === undefined,
                bookingIdIsEmpty: currentBookingId === '',
                amountIsNull: currentAmount === null,
                amountIsUndefined: currentAmount === undefined
            }
        }
    });

    // Critical null checks with detailed logging and backup restoration
    if (currentBookingId === null || currentBookingId === undefined || currentBookingId === '') {
        window.paymentDebug.log('CRITICAL_ERROR_BOOKING_ID_NULL_ATTEMPTING_RESTORE', {
            currentBookingId: currentBookingId,
            type: typeof currentBookingId,
            isNull: currentBookingId === null,
            isUndefined: currentBookingId === undefined,
            isEmpty: currentBookingId === '',
            fullTrace: window.paymentDebug.getTrace()
        });

        // Attempt to restore from backup
        const backup = window.paymentBackup.restore();
        if (backup && backup.bookingId) {
            window.paymentDebug.log('BACKUP_RESTORATION_SUCCESSFUL', {
                backup: backup,
                restoringTo: {
                    currentBookingId: backup.bookingId,
                    currentAmount: backup.amount,
                    currentServiceName: backup.serviceName
                }
            });

            currentBookingId = backup.bookingId;
            currentAmount = backup.amount;
            currentServiceName = backup.serviceName;

            console.warn('Booking ID was null, but successfully restored from backup:', backup);
        } else {
            window.paymentDebug.log('BACKUP_RESTORATION_FAILED', {
                backup: backup,
                fullTrace: window.paymentDebug.getTrace()
            });
            console.error('CRITICAL ERROR: currentBookingId is null/undefined/empty and backup restoration failed');
            alert('Critical Error: Booking ID is null and could not be restored. Please refresh the page and try again.\n\nDebug info logged to console.');
            return;
        }
    }

    if (currentAmount === null || currentAmount === undefined || currentAmount <= 0) {
        window.paymentDebug.log('CRITICAL_ERROR_AMOUNT_INVALID', {
            currentAmount: currentAmount,
            type: typeof currentAmount,
            isNull: currentAmount === null,
            isUndefined: currentAmount === undefined,
            fullTrace: window.paymentDebug.getTrace()
        });
        console.error('CRITICAL ERROR: currentAmount is null/undefined/invalid');
        alert('Critical Error: Amount is invalid. Please refresh the page and try again.');
        return;
    }

    // Robust validation
    if (!currentBookingId || currentBookingId.trim() === '' || currentBookingId === 'null' || currentBookingId === 'undefined') {
        alert('Invalid booking ID. Please refresh the page and try again.');
        console.error('Invalid booking ID:', {
            currentBookingId: currentBookingId,
            type: typeof currentBookingId,
            length: currentBookingId ? currentBookingId.length : 0
        });
        return;
    }

    // Better amount validation
    let parsedAmount;
    if (typeof currentAmount === 'string') {
        const cleanAmount = currentAmount.replace(/[^\d.]/g, '');
        parsedAmount = parseFloat(cleanAmount);
    } else {
        parsedAmount = parseFloat(currentAmount);
    }

    // Round to nearest integer for currency handling
    parsedAmount = Math.round(parsedAmount);

    console.log('Amount validation in processPayment:', {
        currentAmount: currentAmount,
        currentAmountType: typeof currentAmount,
        cleaned: typeof currentAmount === 'string' ? currentAmount.replace(/[^\d.]/g, '') : currentAmount,
        parsed: parsedAmount,
        isNaN: isNaN(parsedAmount),
        lessThanOrEqualZero: parsedAmount <= 0
    });

    if (!currentAmount || isNaN(parsedAmount) || parsedAmount <= 0) {
        alert('Invalid amount. Please refresh the page and try again.');
        console.error('Invalid amount validation failed:', {
            currentAmount: currentAmount,
            type: typeof currentAmount,
            parsed: parsedAmount,
            isNaN: isNaN(parsedAmount),
            lessThanOrEqualZero: parsedAmount <= 0
        });
        return;
    }

    closePaymentModal();
    showLoading();

    try {
        // Ensure data is properly formatted
        const bookingId = String(currentBookingId).trim();
        const amount = parsedAmount; // Use the already validated and parsed amount
        const gatewayUpper = String(gateway).toUpperCase();

        window.paymentDebug.log('DATA_FORMATTING', {
            original: {
                currentBookingId: currentBookingId,
                parsedAmount: parsedAmount,
                gateway: gateway
            },
            formatted: {
                bookingId: bookingId,
                amount: amount,
                gatewayUpper: gatewayUpper
            }
        });

        // Additional validation after parsing
        if (!bookingId || bookingId.length === 0) {
            window.paymentDebug.log('VALIDATION_ERROR_EMPTY_BOOKING_ID', {
                bookingId: bookingId,
                currentBookingId: currentBookingId,
                fullTrace: window.paymentDebug.getTrace()
            });
            throw new Error('Booking ID is empty after processing');
        }

        if (!amount || amount <= 0 || isNaN(amount)) {
            window.paymentDebug.log('VALIDATION_ERROR_INVALID_AMOUNT', {
                amount: amount,
                parsedAmount: parsedAmount,
                currentAmount: currentAmount,
                fullTrace: window.paymentDebug.getTrace()
            });
            throw new Error('Amount is invalid after processing: ' + amount + ' (original: ' + currentAmount + ')');
        }

        const paymentData = {
            booking_id: bookingId,
            amount: amount,
            gateway: gatewayUpper
        };

        window.paymentDebug.log('PAYMENT_DATA_PREPARED', {
            paymentData: paymentData,
            jsonString: JSON.stringify(paymentData),
            currentState: window.paymentDebug.getCurrentState()
        });

        // Create payment record with additional error handling
        const response = await fetch('<?= getBasePath() ?>/api/payments/create.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(paymentData)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const textResponse = await response.text();
            console.error('Non-JSON response:', textResponse);
            throw new Error('Server returned non-JSON response: ' + textResponse.substring(0, 100));
        }

        const result = await response.json();
        console.log('API Response:', result);

        if (!result.success) {
            throw new Error(result.error || 'Failed to create payment');
        }

        // Reset variables before redirect (since we're leaving the page)
        resetPaymentVariables();

        // Redirect to payment processor
        if (gateway === 'dpo') {
            window.location.href = `<?= getBasePath() ?>/customer/payments/dpo.php?payment_id=${result.payment_id}`;
        } else if (gateway === 'stripe') {
            window.location.href = `<?= getBasePath() ?>/customer/payments/stripe.php?payment_id=${result.payment_id}`;
        } else if (gateway === 'flutterwave') {
            window.location.href = `<?= getBasePath() ?>/customer/payments/flutterwave.php?payment_id=${result.payment_id}`;
        }

    } catch (error) {
        hideLoading();
        console.error('Payment processing error:', error);

        // Show more detailed error message
        let errorMessage = 'Payment processing failed: ' + error.message;
        if (error.message.includes('booking_id')) {
            errorMessage += '\n\nDebugging info:\n';
            errorMessage += 'Booking ID: ' + currentBookingId + '\n';
            errorMessage += 'Amount: ' + currentAmount + '\n';
            errorMessage += 'Please refresh the page and try again.';
        }

        alert(errorMessage);
    }
}

async function verifyPayment(paymentId) {
    showLoading();

    try {
        const response = await fetch('<?= getBasePath() ?>/api/payments/verify.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_id: paymentId
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('Payment verified successfully!');
            location.reload();
        } else {
            alert('Payment verification failed: ' + result.error);
        }

    } catch (error) {
        alert('Error verifying payment: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Remove the duplicate event handler - we're using onclick="handlePaymentClick(this)" instead

// Close modal when clicking outside
document.getElementById('paymentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        cancelPayment();
    }
});
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
