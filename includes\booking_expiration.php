<?php
/**
 * Booking Expiration System
 * Handles automatic expiration of old bookings and points management
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/booking_functions.php';

/**
 * Check and expire old bookings
 * This function should be called periodically (via cron or on page loads)
 */
function checkAndExpireBookings() {
    global $database;
    
    try {
        // Get current timestamp
        $now = date('Y-m-d H:i:s');
        
        // Find bookings that should be expired
        $expiredBookings = $database->fetchAll("
            SELECT id, user_id, points_earned, points_used, total_amount
            FROM bookings 
            WHERE status IN ('PENDING', 'CONFIRMED') 
            AND CONCAT(date, ' ', start_time) < ?
            AND date < CURDATE()
        ", [$now]);
        
        if (empty($expiredBookings)) {
            return ['expired_count' => 0, 'points_adjusted' => 0];
        }
        
        $database->beginTransaction();
        
        $expiredCount = 0;
        $totalPointsAdjusted = 0;
        
        foreach ($expiredBookings as $booking) {
            // Update booking status to EXPIRED
            $database->execute("
                UPDATE bookings 
                SET status = 'EXPIRED', updated_at = NOW()
                WHERE id = ?
            ", [$booking['id']]);
            
            // Handle points that were incorrectly awarded during booking creation
            if ($booking['points_earned'] > 0) {
                // Remove points from user account
                $database->execute("
                    UPDATE users 
                    SET points = GREATEST(0, points - ?), updated_at = NOW()
                    WHERE id = ?
                ", [$booking['points_earned'], $booking['user_id']]);
                
                // Record point deduction transaction
                try {
                    $database->execute("
                        INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
                        VALUES (?, ?, ?, ?, 'DEDUCTION', 'Points removed due to booking expiration', NOW())
                    ", [
                        generateUUID(),
                        $booking['user_id'],
                        $booking['id'],
                        -$booking['points_earned']
                    ]);
                } catch (Exception $e) {
                    // Fallback for tables without booking_id column
                    if (strpos($e->getMessage(), "Unknown column 'booking_id'") !== false) {
                        error_log("Point transactions table missing booking_id column, using fallback for expiration deduction");
                        $database->execute("
                            INSERT INTO point_transactions (id, user_id, points, type, description, created_at)
                            VALUES (?, ?, ?, 'DEDUCTION', 'Points removed due to booking expiration', NOW())
                        ", [
                            generateUUID(),
                            $booking['user_id'],
                            -$booking['points_earned']
                        ]);
                    } else {
                        throw $e; // Re-throw if it's a different error
                    }
                }
                
                // Update booking to remove earned points
                $database->execute("
                    UPDATE bookings 
                    SET points_earned = 0, updated_at = NOW()
                    WHERE id = ?
                ", [$booking['id']]);
                
                $totalPointsAdjusted += $booking['points_earned'];
            }
            
            // Return used points to customer (if any were used)
            if ($booking['points_used'] > 0) {
                $database->execute("
                    UPDATE users 
                    SET points = points + ?, updated_at = NOW()
                    WHERE id = ?
                ", [$booking['points_used'], $booking['user_id']]);
                
                // Record point return transaction
                $database->execute("
                    INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
                    VALUES (?, ?, ?, ?, 'REFUND', 'Points refunded due to booking expiration', NOW())
                ", [
                    generateUUID(),
                    $booking['user_id'],
                    $booking['id'],
                    $booking['points_used']
                ]);
            }
            
            $expiredCount++;
        }
        
        // Update last expiration check timestamp
        $database->execute("
            INSERT INTO system_settings (setting_key, setting_value) 
            VALUES ('last_expiration_check', NOW()) 
            ON DUPLICATE KEY UPDATE setting_value = NOW()
        ");
        
        $database->commit();
        
        return [
            'expired_count' => $expiredCount,
            'points_adjusted' => $totalPointsAdjusted
        ];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Booking expiration error: " . $e->getMessage());
        throw $e;
    }
}

// awardPointsForCompletedBooking function moved to booking_functions.php to avoid duplication

/**
 * Check if expiration check should run (to avoid running too frequently)
 */
function shouldRunExpirationCheck() {
    global $database;
    
    try {
        $lastCheck = $database->fetch("
            SELECT setting_value 
            FROM system_settings 
            WHERE setting_key = 'last_expiration_check'
        ");
        
        if (!$lastCheck) {
            return true; // Never checked before
        }
        
        $lastCheckTime = strtotime($lastCheck['setting_value']);
        $now = time();
        
        // Run check if last check was more than 1 hour ago
        return ($now - $lastCheckTime) > 3600;
        
    } catch (Exception $e) {
        error_log("Expiration check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Run expiration check if needed (call this on page loads)
 * Note: This handles booking expiration, not reminders. Reminders are handled separately.
 */
function runExpirationCheckIfNeeded() {
    // Check if automatic expiration checks are disabled (for production with cron jobs)
    if (defined('DISABLE_AUTO_EXPIRATION_CHECK') && DISABLE_AUTO_EXPIRATION_CHECK === true) {
        return null; // Skip automatic checks, rely on cron job instead
    }

    if (shouldRunExpirationCheck()) {
        try {
            return checkAndExpireBookings();
        } catch (Exception $e) {
            error_log("Auto expiration check failed: " . $e->getMessage());
            return null;
        }
    }
    return null;
}

/**
 * Get booking status display information
 */
function getBookingStatusInfo($status) {
    $statusInfo = [
        'PENDING' => [
            'label' => 'Pending',
            'class' => 'bg-yellow-100 text-yellow-800',
            'icon' => 'fas fa-clock'
        ],
        'CONFIRMED' => [
            'label' => 'Confirmed',
            'class' => 'bg-blue-100 text-blue-800',
            'icon' => 'fas fa-check-circle'
        ],
        'IN_PROGRESS' => [
            'label' => 'In Progress',
            'class' => 'bg-purple-100 text-purple-800',
            'icon' => 'fas fa-play-circle'
        ],
        'COMPLETED' => [
            'label' => 'Completed',
            'class' => 'bg-green-100 text-green-800',
            'icon' => 'fas fa-check-double'
        ],
        'CANCELLED' => [
            'label' => 'Cancelled',
            'class' => 'bg-red-100 text-red-800',
            'icon' => 'fas fa-times-circle'
        ],
        'NO_SHOW' => [
            'label' => 'No Show',
            'class' => 'bg-gray-100 text-gray-800',
            'icon' => 'fas fa-user-slash'
        ],
        'EXPIRED' => [
            'label' => 'Expired',
            'class' => 'bg-orange-100 text-orange-800',
            'icon' => 'fas fa-hourglass-end'
        ]
    ];
    
    return $statusInfo[$status] ?? $statusInfo['PENDING'];
}
?>
