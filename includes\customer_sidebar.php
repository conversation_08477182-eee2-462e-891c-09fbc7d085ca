<?php
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();

// Normalize current path for comparison
$normalizedCurrentPath = rtrim(parse_url($currentPath, PHP_URL_PATH), '/');
$customerBasePath = $basePath . '/customer';
$navigation = [
    [
        'name' => 'Dashboard',
        'href' => $basePath . '/customer',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />'
    ],
    [
        'name' => 'Book Appointment',
        'href' => $basePath . '/customer/book',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />'
    ],
    [
        'name' => 'My Bookings',
        'href' => $basePath . '/customer/bookings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />'
    ],
    [
        'name' => 'Points & Rewards',
        'href' => $basePath . '/customer/rewards',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />'
    ],
    [
        'name' => 'Profile',
        'href' => $basePath . '/customer/profile',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />'
    ]
];
?>

<nav class="space-y-1">
    <?php foreach ($navigation as $item):
        // Normalize item href for comparison
        $normalizedItemHref = rtrim($item['href'], '/');

        // Check if this menu item is active
        $isActive = false;

        // Exact match for all pages
        if ($normalizedCurrentPath === $normalizedItemHref) {
            $isActive = true;
        }
        // For non-dashboard items, check sub-pages
        elseif ($normalizedItemHref !== $customerBasePath && strpos($normalizedCurrentPath . '/', $normalizedItemHref . '/') === 0) {
            $isActive = true;
        }
    ?>
        <a href="<?= $item['href'] ?>"
           class="<?= $isActive
               ? 'bg-salon-gold text-black shadow-lg'
               : 'text-gray-300 hover:bg-secondary-800 hover:text-salon-gold border border-transparent hover:border-salon-gold/20'
           ?> group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-300 hover-lift">
            <svg class="<?= $isActive ? 'text-black' : 'text-gray-400 group-hover:text-salon-gold' ?> mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-300"
                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <?= $item['icon'] ?>
            </svg>
            <?= $item['name'] ?>
        </a>
    <?php endforeach; ?>
</nav>

<!-- Quick Stats -->
<div class="mt-8 pt-8 border-t border-secondary-700">
    <h3 class="text-xs font-semibold text-salon-gold uppercase tracking-wider mb-4">Account Summary</h3>
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-lg p-4 space-y-3">
        <?php
        // Get customer quick stats
        $customerId = $_SESSION['user_id'];
        $upcomingBookings = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE user_id = ? AND date >= CURDATE() AND status IN ('PENDING', 'CONFIRMED')", [$customerId])['count'];
        $totalBookings = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE user_id = ?", [$customerId])['count'];
        $totalSpent = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE user_id = ? AND status = 'COMPLETED'", [$customerId])['total'] ?? 0;
        ?>

        <div class="flex items-center justify-between text-sm">
            <span class="text-gray-400">Points Balance</span>
            <span class="text-salon-gold font-semibold"><?= number_format($customerPoints['currentPoints']) ?></span>
        </div>

        <div class="flex items-center justify-between text-sm">
            <span class="text-gray-400">Upcoming</span>
            <span class="text-salon-gold font-semibold"><?= $upcomingBookings ?></span>
        </div>

        <div class="flex items-center justify-between text-sm">
            <span class="text-gray-400">Total Visits</span>
            <span class="text-white font-semibold"><?= $totalBookings ?></span>
        </div>

        <div class="flex items-center justify-between text-sm">
            <span class="text-gray-400">Total Spent</span>
            <span class="text-salon-gold font-semibold"><?= formatCurrency($totalSpent) ?></span>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-8 pt-8 border-t border-secondary-700">
    <h3 class="text-xs font-semibold text-salon-gold uppercase tracking-wider mb-4">Quick Actions</h3>
    <div class="space-y-3">
        <a href="<?= $basePath ?>/customer/book" class="w-full flex items-center justify-center px-4 py-3 bg-salon-gold text-black hover:bg-gold-light rounded-lg text-sm font-semibold transition-all duration-300 hover-lift shadow-lg">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Book Now
        </a>

        <a href="<?= $basePath ?>/customer/bookings" class="w-full flex items-center justify-center px-4 py-3 border border-secondary-700 text-gray-300 hover:bg-secondary-800 hover:text-salon-gold hover:border-salon-gold/50 rounded-lg text-sm font-medium transition-all duration-300 hover-lift">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            View Bookings
        </a>
    </div>
</div>
