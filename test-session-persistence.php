<?php
/**
 * Test Session Persistence
 * Flix Salonce - PHP Version
 * Use this to test if sessions persist after browser close
 */

require_once 'config/app.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Persistence Test - Flix Salon & SPA</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        .status-ok { color: green; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .status-warning { color: orange; font-weight: bold; }
        .code { background: #f8f8f8; padding: 10px; border-radius: 3px; font-family: monospace; }
        .button { display: inline-block; padding: 10px 20px; background: #d4af37; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .button:hover { background: #b8941f; }
        .instructions { background: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Session Persistence Test</h1>
        
        <div class="instructions">
            <h3>How to Test Session Persistence:</h3>
            <ol>
                <li><strong>Step 1:</strong> Log in to your account if not already logged in</li>
                <li><strong>Step 2:</strong> Note the session information below</li>
                <li><strong>Step 3:</strong> Close your browser completely (all windows/tabs)</li>
                <li><strong>Step 4:</strong> Reopen your browser and visit this page again</li>
                <li><strong>Step 5:</strong> Check if you're still logged in and session data persists</li>
            </ol>
        </div>

        <div class="section">
            <h3>Current Session Status</h3>
            <p><strong>Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
            <p><strong>Environment:</strong> <?= isProduction() ? 'PRODUCTION' : 'DEVELOPMENT' ?></p>
            
            <?php if (isLoggedIn()): ?>
                <p class="status-ok">✅ You are currently logged in</p>
                <div class="code">
                    <strong>User ID:</strong> <?= $_SESSION['user_id'] ?? 'Not set' ?><br>
                    <strong>User Role:</strong> <?= $_SESSION['user_role'] ?? 'Not set' ?><br>
                    <strong>User Name:</strong> <?= $_SESSION['user_name'] ?? 'Not set' ?><br>
                    <strong>Session Token:</strong> <?= isset($_SESSION['session_token']) ? substr($_SESSION['session_token'], 0, 8) . '...' : 'Not set' ?><br>
                    <strong>Session ID:</strong> <?= session_id() ?><br>
                    <strong>Last Activity:</strong> <?= isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : 'Not set' ?>
                </div>
                
                <?php if (hasRole('ADMIN')): ?>
                    <p><a href="<?= getBasePath() ?>/admin" class="button">Go to Admin Dashboard</a></p>
                <?php elseif (hasRole('STAFF')): ?>
                    <p><a href="<?= getBasePath() ?>/staff" class="button">Go to Staff Dashboard</a></p>
                <?php else: ?>
                    <p><a href="<?= getBasePath() ?>/customer" class="button">Go to Customer Dashboard</a></p>
                <?php endif; ?>
                
                <p><a href="<?= getBasePath() ?>/auth/logout.php" class="button" style="background: #dc3545;">Logout</a></p>
                
            <?php else: ?>
                <p class="status-error">❌ You are not logged in</p>
                <p>Please log in first to test session persistence.</p>
                <p><a href="<?= getBasePath() ?>/auth/login.php" class="button">Login</a></p>
            <?php endif; ?>
        </div>

        <div class="section">
            <h3>Session Configuration</h3>
            <div class="code">
                <strong>Cookie Lifetime:</strong> <?= ini_get('session.cookie_lifetime') ?> seconds (<?= round(ini_get('session.cookie_lifetime') / 86400, 1) ?> days)<br>
                <strong>GC Max Lifetime:</strong> <?= ini_get('session.gc_maxlifetime') ?> seconds (<?= round(ini_get('session.gc_maxlifetime') / 86400, 1) ?> days)<br>
                <strong>Cookie Secure:</strong> <?= ini_get('session.cookie_secure') ? 'YES' : 'NO' ?><br>
                <strong>Cookie HTTPOnly:</strong> <?= ini_get('session.cookie_httponly') ? 'YES' : 'NO' ?><br>
                <strong>Cookie SameSite:</strong> <?= ini_get('session.cookie_samesite') ?: 'Not set' ?><br>
                <strong>Use Only Cookies:</strong> <?= ini_get('session.use_only_cookies') ? 'YES' : 'NO' ?>
            </div>
        </div>

        <div class="section">
            <h3>HTTPS Status</h3>
            <?php 
            $isHttps = (
                (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ||
                (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
                (isset($_SERVER['HTTP_X_FORWARDED_SSL']) && $_SERVER['HTTP_X_FORWARDED_SSL'] === 'on') ||
                (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443)
            );
            ?>
            <p class="<?= $isHttps ? 'status-ok' : 'status-warning' ?>">
                <?= $isHttps ? '🔒 HTTPS Detected' : '⚠️ HTTP Detected (sessions may not persist properly)' ?>
            </p>
            <div class="code">
                <strong>$_SERVER['HTTPS']:</strong> <?= $_SERVER['HTTPS'] ?? 'Not set' ?><br>
                <strong>$_SERVER['HTTP_X_FORWARDED_PROTO']:</strong> <?= $_SERVER['HTTP_X_FORWARDED_PROTO'] ?? 'Not set' ?><br>
                <strong>$_SERVER['SERVER_PORT']:</strong> <?= $_SERVER['SERVER_PORT'] ?? 'Not set' ?>
            </div>
        </div>

        <?php if (isLoggedIn() && isset($auth)): ?>
        <div class="section">
            <h3>Database Session Check</h3>
            <?php
            try {
                $dbSession = $database->fetch(
                    "SELECT * FROM sessions WHERE session_token = ? AND expires > NOW()",
                    [$_SESSION['session_token']]
                );
                
                if ($dbSession): ?>
                    <p class="status-ok">✅ Session found in database and not expired</p>
                    <div class="code">
                        <strong>Session ID:</strong> <?= $dbSession['id'] ?><br>
                        <strong>User ID:</strong> <?= $dbSession['user_id'] ?><br>
                        <strong>Expires:</strong> <?= $dbSession['expires'] ?><br>
                        <strong>Created:</strong> <?= $dbSession['created_at'] ?>
                    </div>
                <?php else: ?>
                    <p class="status-error">❌ Session not found in database or expired</p>
                <?php endif;
            } catch (Exception $e) { ?>
                <p class="status-error">❌ Error checking database session: <?= htmlspecialchars($e->getMessage()) ?></p>
            <?php } ?>
        </div>
        <?php endif; ?>

        <div class="section">
            <h3>Browser Cookies</h3>
            <?php if (!empty($_COOKIE)): ?>
                <div class="code">
                    <?php foreach ($_COOKIE as $name => $value): ?>
                        <strong><?= htmlspecialchars($name) ?>:</strong> 
                        <?php if (strpos($name, 'PHPSESSID') !== false): ?>
                            <?= substr($value, 0, 8) ?>...
                        <?php else: ?>
                            <?= htmlspecialchars(substr($value, 0, 30)) ?><?= strlen($value) > 30 ? '...' : '' ?>
                        <?php endif; ?><br>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="status-warning">⚠️ No cookies found</p>
            <?php endif; ?>
        </div>

        <div class="section">
            <h3>Expected Behavior</h3>
            <ul>
                <li>✅ Sessions should persist for 2 weeks (1,209,600 seconds)</li>
                <li>✅ Users should remain logged in after closing browser</li>
                <li>✅ Session should extend automatically on activity</li>
                <li>✅ Logout should completely clear session and cookies</li>
            </ul>
        </div>

        <div class="section">
            <h3>Troubleshooting</h3>
            <p>If sessions are not persisting:</p>
            <ul>
                <li>Check if HTTPS is properly configured in production</li>
                <li>Verify browser cookie settings allow persistent cookies</li>
                <li>Check server PHP session configuration</li>
                <li>Ensure session directory is writable</li>
                <li>Check for any proxy/CDN interference with cookies</li>
            </ul>
        </div>

        <div class="section">
            <p><a href="?refresh=1" class="button">🔄 Refresh Test</a></p>
            <?php if (hasRole('ADMIN')): ?>
                <p><a href="<?= getBasePath() ?>/admin/session-diagnostic.php" class="button">🔧 Advanced Diagnostics</a></p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
